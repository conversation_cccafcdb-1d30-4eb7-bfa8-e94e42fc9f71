import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:intl/intl.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/views/notification_details/notification_details_view.dart';
import 'package:onboard_wallet/ui/views/notifications/widgets/more_option.dart';
import 'package:onboard_wallet/ui/views/push_notification_modal/push_notification_modal_view.dart';
import 'package:onboard_wallet/ui/views/wallet/subviews/ongoing_order_card.dart';
import 'package:onboard_wallet/utils/utils.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../widgets/widgets.dart';

class NotificationsViewModel extends ReactiveViewModel {
  final _inboxService = locator<InboxService>();
  final _navigationService = locator<NavigationService>();
  final PagingController<int, MessageGroup> pagingController =
      PagingController(firstPageKey: 1);
  bool hasUnreadMessage = false;

  final _ongoingOrderService = locator<OngoingOrderService>();

  bool get isEmpty => pagingController.itemList?.isEmpty ?? true;

  @override
  List<ListenableServiceMixin> get listenableServices {
    return [_ongoingOrderService];
  }

  List<Widget> get bannerItems {
    return [
      if (_ongoingOrderService.hasActionRequiredCustomerOrder) ...[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: DisputeOrderCard(
            subtitle: _ongoingOrderService.customerDisputeOrderSubtitle,
            title: S.current.yourOrderNeedsAttention(
                _ongoingOrderService.customerDisputeOrdersLength),
            onTap: () => _ongoingOrderService.toCustomerDisputeOrderWebView(),
          ),
        )
      ],
      if (_ongoingOrderService.hasActionRequiredMerchantOrder) ...[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: DisputeOrderCard(
            subtitle: _ongoingOrderService.merchantDisputeOrderSubtitle,
            title: S.current.yourOrderNeedsAttention(
                _ongoingOrderService.merchantDisputeOrdersLength),
            onTap: () => _ongoingOrderService.toMerchantDisputeOrderWebView(),
          ),
        )
      ],
      if (_ongoingOrderService.hasOngoingMerchantOrder) ...[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: OngoingOrderCard(
            subtitle: _ongoingOrderService.merchantOngoingOrderSubtitle,
            onTap: () => _ongoingOrderService.toMerchantOngoingOrderWebView(),
            progress: _ongoingOrderService.merchantOrderProgress,
          ),
        )
      ],
      if (_ongoingOrderService.hasOngoingCustomerOrder) ...[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: OngoingOrderCard(
            subtitle: _ongoingOrderService.customerOngoingOrderSubtitle,
            onTap: () => _ongoingOrderService.toCustomerOngoingOrderWebView(),
            progress: _ongoingOrderService.totalOrderProgress,
          ),
        )
      ],
    ];
  }

  int get unReadCount {
    int count = 0;
    final messageGroups = pagingController.itemList ?? [];
    for (final group in messageGroups) {
      final messages = group.messages;
      for (final message in messages) {
        if (message.isRead == false) {
          count += 1;
        }
      }
    }
    return count;
  }

  void onReady() {
    pagingController.addPageRequestListener((pageKey) {
      getMessages(pageKey);
    });
  }

  void _setUnreadValue() {
    bool hasUnread = false;
    final messageGroups = pagingController.itemList ?? [];
    for (final group in messageGroups) {
      final messages = group.messages;
      for (final message in messages) {
        if (message.isRead == false) {
          hasUnread = true;
          break;
        }
      }
    }
    hasUnreadMessage = hasUnread;
    notifyListeners();
  }

  Future getMessages(int page) async {
    final messages = await _inboxService.getMessages(page: page, size: 20);
    final isLastPage = messages.length < 20;
    // Remove duplicate
    messages.removeWhere((element) {
      final index = messages.indexWhere((p0) => p0.id == element.id);
      return index != -1 && index != messages.indexOf(element);
    });
    Map<String, List<InboxMessage>> groupedMessages = {};
    Map<String, DateTime> groupedDateTimes = {};
    for (final message in messages) {
      final date = DateUtils.dateOnly(message.receivedAt ?? DateTime.now());
      String dateString = DateFormat('yyyy-MM-dd').format(date);
      if (groupedMessages.containsKey(dateString)) {
        groupedMessages[dateString]!.add(message);
        groupedDateTimes[dateString] = date;
      } else {
        groupedMessages[dateString] = [message];
        groupedDateTimes[dateString] = date;
      }
    }
    List<MessageGroup> messageGroups = groupedMessages.entries
        .map((entry) => MessageGroup(
            date: entry.key,
            dateTime: groupedDateTimes[entry.key]!,
            messages: entry.value))
        .toList();
    if (page == 0) {
      if (isLastPage) {
        pagingController.appendLastPage(messageGroups);
      } else {
        final nextPageKey = page + 1;
        pagingController.appendPage(messageGroups, nextPageKey);
      }
    } else {
      final currentList = pagingController.itemList ?? [];
      final newGroups = <MessageGroup>[];
      for (final group in messageGroups) {
        final existingGroupIndex = currentList.indexWhere((existingGroup) {
          return existingGroup.date == group.date;
        });
        if (existingGroupIndex != -1) {
          currentList[existingGroupIndex].messages.addAll(group.messages);
        } else {
          newGroups.add(group);
        }
      }
      if (isLastPage) {
        pagingController.appendLastPage(newGroups);
      } else {
        final nextPageKey = page + 1;
        pagingController.appendPage(newGroups, nextPageKey);
      }
    }
    _setUnreadValue();
  }

  void refresh() {
    pagingController.itemList?.clear();
    getMessages(1);
    notifyListeners();
  }

  onNotificationSelected(InboxMessage message) {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    markAsRead(ids: [message.id]);
    if (MessageGroup.isGeneralNotification(message)) {
      _openGeneralNotification(message);
      return;
    }
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      clipBehavior: Clip.hardEdge,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(24),
        ),
      ),
      builder: (context) {
        return FractionallySizedBox(
          heightFactor: 0.9,
          child: NotificationDetailsView(message: message),
        );
      },
    );
  }

  void _openGeneralNotification(InboxMessage message) {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    final map = MessageGroup.convertBuiltMapToMap(message);
    if (map == null) return;
    if (context != null) {
      ExternalCloseSheet.showModal(
        context,
        child: PushNotificationModelView(
          data: map,
          notificationId: message.id,
        ),
      );
    }
  }

  void onMorePressed() {
    if (isEmpty) return;
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      backgroundColor: Grey.grey25,
      child: NotificationMoreOptions(
        markAllAsRead: () {
          _navigationService.back();
          hasUnreadMessage = false;
          notifyListeners();
          var items = pagingController.itemList ?? [];
          List<String> messageIds = [];
          for (final item in items) {
            final messages = item.messages;
            for (var message in messages) {
              if (message.isRead != true) {
                messageIds.add(message.id);
              }
            }
          }
          if (messageIds.isNotEmpty) {
            markAsRead(ids: messageIds);
          }
        },
      ),
    );
  }

  Future markAsRead({required List<String> ids}) async {
    var items = pagingController.itemList ?? [];
    List<MessageGroup> groups = [];
    for (final item in items) {
      final messages = item.messages;
      List<InboxMessage> updatedMessages = [];
      for (var message in messages) {
        if (ids.contains(message.id)) {
          message = message.rebuild((p0) => p0.isRead = true);
        }
        updatedMessages.add(message);
      }
      item.messages = updatedMessages;
      groups.add(item);
    }
    pagingController.itemList = groups;
    _setUnreadValue();
    notifyListeners();
    await _inboxService.markAsRead(messageIds: ids);
  }
}

class MessageGroup {
  final String date;
  final DateTime dateTime;
  List<InboxMessage> messages;

  MessageGroup(
      {required this.date, required this.dateTime, required this.messages});

  static bool isTransactionNotification(InboxMessage inboxMessage) {
    final data = inboxMessage.data?.asMap();
    if (data == null) return false;
    final category = data["category"]?.asString;
    return category == "token-transfer" || category == "coin-transfer";
  }

  static bool isCardTransaction(InboxMessage inboxMessage) {
    final data = inboxMessage.data?.asMap();
    if (data == null) return false;
    final category = data["topic"]?.asString;
    return category == "cards";
  }

  static bool isOrderTransaction(InboxMessage inboxMessage) {
    final data = inboxMessage.data?.asMap();
    if (data == null) return false;
    final category = data["topic"]?.asString;
    final target = data['target']?.asString;
    return category == "orders" || target == 'view-bridge-order';
  }

  static String? getOrderLink(InboxMessage inboxMessage) {
    final data = inboxMessage.data?.asMap();
    if (data == null) return null;
    final ctaUrl = data["ctaUrl"]?.asString;
    return ctaUrl;
  }

  static String? getTransactionHash(InboxMessage inboxMessage) {
    final data = inboxMessage.data?.asMap();
    if (data == null) return null;
    final hash = data["hash"]?.asString;
    return hash;
  }

  static String? getTransactionNetworkId(InboxMessage inboxMessage) {
    final data = inboxMessage.data?.asMap();
    if (data == null) return null;
    final network = data["network"]?.asString;
    return network;
  }

  static String? getTransactionToken(InboxMessage inboxMessage) {
    final data = inboxMessage.data?.asMap();
    if (data == null) return null;
    final tokens = data["tokens"]?.asList;
    final tokenContract = tokens?.firstOrNull;
    return tokenContract?.toString();
  }

  static String? getTransactionTokenContract(InboxMessage inboxMessage) {
    final data = inboxMessage.data?.asMap();
    if (data == null) return null;
    final network = data["tokens"]?.asList;
    if (network == null) return null;
    if (network.isEmpty) return null;
    return network.first;
  }

  static String? getAddress(InboxMessage inboxMessage) {
    final data = inboxMessage.data?.asMap();
    if (data == null) return null;
    final address = data["address"]?.asString;
    return address;
  }

  static Widget iconForMessage(InboxMessage message) {
    if (MessageGroup.isTransactionNotification(message)) {
      return Assets.svg.withdrawaNotificationIcon.svg();
    } else if (MessageGroup.isCardTransaction(message)) {
      return Assets.svg.cardNotification.svg();
    }
    return Assets.svg.genericCryptoTranx.svg();
  }

  static bool isGeneralNotification(InboxMessage message) {
    final data = message.data;
    if (data == null) return false;
    if (!data.containsKey("notificationType")) return false;
    String? notificationType = data["notificationType"]?.asString;
    return notificationType == "general";
  }

  static Map<String, dynamic>? convertBuiltMapToMap(InboxMessage message) {
    return message.data
        ?.map((key, value) => MapEntry(key, value?.asString))
        .toMap();
  }

  static String? getCtaBasedOnTarget(InboxMessage message) {
    final data = message.data?.asMap();
    if (data == null) return null;
    final target = data["target"]?.asString;
    if (isNullOrEmpty(target)) return null;
    switch (target!) {
      case NotificationTarget.createAd:
        return S.current.createNewAd;
      case NotificationTarget.home:
        return S.current.proceed;
      case NotificationTarget.orderSummary:
        return S.current.viewDetails;
      case NotificationTarget.viewAd:
      case NotificationTarget.viewAds:
        return S.current.viewAd;
      case NotificationTarget.viewChat:
        return S.current.viewMessage;
      case NotificationTarget.viewNetwork:
        return S.current.viewNetwork;
      case NotificationTarget.viewBroadcast:
        return S.current.viewBroadcast;
      case NotificationTarget.viewOffer:
        return S.current.viewOffer;
      case NotificationTarget.viewNetworkRequest:
        return S.current.viewRequest;
      default:
        return null;
    }
  }
}
