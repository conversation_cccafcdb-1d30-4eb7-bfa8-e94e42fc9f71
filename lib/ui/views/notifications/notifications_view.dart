import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:intl/intl.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/extensions/date_time.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/widgets/app_bar/app_bar.dart';
import 'package:onboard_wallet/ui/widgets/cards/card_surface.dart';
import 'package:onboard_wallet/ui/widgets/cards/onboard_advert_cards.dart';
import 'package:stacked/stacked.dart';

import 'notifications_viewmodel.dart';

class NotificationsView extends StackedView<NotificationsViewModel> {
  const NotificationsView({super.key});

  @override
  Widget builder(
    BuildContext context,
    NotificationsViewModel viewModel,
    Widget? child,
  ) {
    final textTheme = Theme.of(context).textTheme;
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: TransparentAppBar(
        centerTitle: true,
        title: Text(
          S.current.notifications,
          style: textTheme.body18Medium.copyWith(
            fontWeight: FontWeight.w500,
            color: Grey.grey90,
          ),
        ),
        actions: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: viewModel.onMorePressed,
            child: SizedBox(
              width: 48,
              height: 48,
              child: Center(
                child: Assets.svg.moreHorizontal.svg(
                  colorFilter: ColorFilter.mode(
                    viewModel.isEmpty ? Grey.grey400 : Colors.black,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(
            width: 10,
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            const verticalSpace(17),
            if (viewModel.bannerItems.isNotEmpty) ...[
              SizedBox(
                height: 105,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: OnboardAdvertCards(
                    autoPlay: false,
                    bannerItems: viewModel.bannerItems,
                  ),
                ),
              ),
              const verticalSpace(34)
            ],
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async => viewModel.pagingController.refresh(),
                child: Container(
                  padding: const EdgeInsets.only(
                      left: 16.0, right: 16.0, bottom: 21),
                  child: PagedListView<int, MessageGroup>.separated(
                    pagingController: viewModel.pagingController,
                    builderDelegate: PagedChildBuilderDelegate(
                        itemBuilder: (context, messageGroup, index) =>
                            MessageGroupView(
                              messageGroup: messageGroup,
                              onSelectMessage: viewModel.onNotificationSelected,
                            ),
                        noItemsFoundIndicatorBuilder: (context) {
                          return Column(
                            children: [
                              const SizedBox(
                                height: 45,
                              ),
                              Assets.svg.emptyNotification.svg(),
                              const SizedBox(
                                height: 8,
                              ),
                              Text(
                                S.current.emptyNotifications,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyLarge
                                    ?.copyWith(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Grey.grey700,
                                      height: 22 / 14,
                                    ),
                              )
                            ],
                          );
                        }),
                    separatorBuilder: (BuildContext context, int index) {
                      return const SizedBox(
                        height: 30,
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  NotificationsViewModel viewModelBuilder(
    BuildContext context,
  ) =>
      locator<NotificationsViewModel>();

  @override
  bool get disposeViewModel => false;
}

class MessageGroupView extends StatelessWidget {
  const MessageGroupView(
      {super.key, required this.messageGroup, required this.onSelectMessage});

  final MessageGroup messageGroup;
  final Function(InboxMessage) onSelectMessage;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _formatDate(messageGroup.dateTime),
          style: textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 16.fontSize,
            color: Grey.grey500,
            height: 24 / 16,
          ),
        ),
        const SizedBox(
          height: 18,
        ),
        CardSurface(
          borderRadius: BorderRadius.circular(14),
          padding: const EdgeInsets.all(4),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final message = messageGroup.messages[index];
              return CardSurface(
                borderRadius: BorderRadius.circular(12),
                padding: const EdgeInsets.all(10),
                bgColor: message.isRead == true
                    ? Colors.transparent
                    : const Color(0xff5720F5).withOpacity(0.03),
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () => onSelectMessage(message),
                  child: Row(
                    children: [
                      MessageGroup.iconForMessage(message),
                      const SizedBox(
                        width: 16,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              message.title,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: textTheme.body14Medium.copyWith(
                                color: Grey.grey900,
                              ),
                            ),
                            const verticalSpace(6),
                            Text(
                              message.body,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: textTheme.body12Regular.copyWith(
                                color: Grey.grey400,
                                height: 18 / 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                      if (message.isRead == false) Assets.svg.redDot.svg()
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (context, _) => const SizedBox(
              height: 20,
            ),
            itemCount: messageGroup.messages.length,
          ),
        ),
      ],
    );
  }
}

String _formatDate(DateTime date) {
  if (date.isToday) {
    return S.current.today;
  } else if (date.isYesterday) {
    return S.current.yesterday;
  } else if (date.isThisYear) {
    var formatter = DateFormat("dd MMM");
    return formatter.format(date);
  } else {
    var formatter = DateFormat("dd MMM yyyy");
    return formatter.format(date);
  }
}
