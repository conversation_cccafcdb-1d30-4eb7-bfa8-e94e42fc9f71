import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:nestcoinco_onboard_bridge_integration/nestcoinco_onboard_bridge_integration.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/analytic_event.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/extensions/num.dart';
import 'package:onboard_wallet/extensions/string.dart';
import 'package:onboard_wallet/manager/toast.dart';

import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/models/swing/swing_route_request_body/swing_route_request_body.dart';

import 'package:onboard_wallet/services/send_service.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/dialogs/info_alert/external_close_dialog.dart';
import 'package:onboard_wallet/ui/views/send/base_send_view_model.dart';
import 'package:onboard_wallet/ui/views/send/data/send_asset_model.dart';
import 'package:onboard_wallet/ui/views/send/data/send_destination_enum.dart';
import 'package:onboard_wallet/ui/widgets/bottomsheet/external_close_sheet.dart';

import 'package:onboard_wallet/ui/widgets/container/token_logo_container.dart';
import 'package:onboard_wallet/ui/widgets/onboard_fee_explainer.dart';
import 'package:onboard_wallet/utils/input_formatters.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../../enums/kyc_purpose.dart';
import '../../transfer/internal/data/spot_wallet.dart';
import '../choose_defi_wallet_token/choose_defi_wallet_token_view.dart';
import 'confirm_send_via_swap/confirm_send_via_swap_view.dart';
import 'confirm_send_via_swap/confirm_send_via_swap_viewmodel.dart';
import 'widgets/swap_warning_modal.dart';

class SendFromDefiViewmodel extends BaseSendViewModel {
  final _navigationService = locator<NavigationService>();
  final _sendService = locator<SendService>();
  final _toastManager = locator<ToastManager>();
  final _userService = locator<UserService>();
  final _kycService = locator<KycService>();
  final _networkService = locator<NetworkService>();
  final _swapService = locator<SwapService>();
  final _crossSwapService = locator<CrossSwapService>();
  final _analyticsService = locator<AnalyticsService>();

  OnboardUser? get currentUser => _userService.getCurrentUser();

  SendFromDefiViewmodel(
      {required super.sendDestinationEnum, required super.transferAccountType});

  List<TextInputFormatter> get fromInputFormatters =>
      [AnyDpDecimalTextInputFormatter()];

  List<TextInputFormatter> get toInputFormatters => [
        if (sendDestinationEnum == SendDestinationEnum.bankAccount) ...[
          AnyDpDecimalTextInputFormatter()
        ] else ...[
          AnyDpDecimalTextInputFormatter()
        ]
      ];

  LocalNetwork? fromNetwork;

  String? get fromTokenBalance => sourceToken?.balance == null
      ? null
      : sourceToken
          ?.balanceAsDouble()
          .currencyFormat(decimalDigits: 4, symbol: "")
          .removeTrailingZero();

  FiatTransferAsset? destinationFiatTransferAsset;
  CryptoTransferAsset? sourceCryptoTransferAsset;
  FiatTransferAssetsList? fiatTransferAssetsList;
  CryptoTransferAssetsList? cryptoTransferAssetsList;

  List<LocalToken> networkTokens = [];
  LocalTokenNetworkBalance? fromLocalTokenNetworkBalance;
  LocalAddressTokenBalance? fromLocalAddressTokenBalance;

  @override
  void showFeeExplainer() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseDialog.showExternalDialog(
      context,
      child: const OnboardFeeExplainer(),
    );
  }

  changeFromAsset() {
    _toChooseFromFiatView();
  }

  _checkInsufficientBalance() {
    final amount = fromAmountAsDouble;
    if (amount == null) {
      insufficientBalance = false;
      return;
    }
    ;
    if (amount == 0) {
      insufficientBalance = false;
      return;
    }
    insufficientBalance = sourceToken?.balanceAsDouble() != null &&
        amount > sourceToken!.balanceAsDouble();
  }

  _toChooseFromFiatView() {
    _navigationService.navigateToView(
      ChooseDefiWalletTokenView(
        onSelectToken:
            (fromLocalAddressTokenBalance, fromLocalTokenNetworkBalance) {
          fromTextEditingController.clear();
          onFromInputChanged(null);
          _analyticsService.logEvent(
              eventName: AnalyticsEvent.selectSourceCryptoCurrency,
              parameters: {
                EventParameterKey.network: fromLocalTokenNetworkBalance.name,
                EventParameterKey.tokenSymbol:
                    fromLocalAddressTokenBalance.symbol
              });

          /// Get network with all the fields using LocalTokenNetworkBalance id
          final supportedNetwork = _networkService
              .getChainByNetworkId(fromLocalTokenNetworkBalance.id ?? "");
          final localToken = LocalToken.fromAddressAndNetworkBalance(
              fromLocalAddressTokenBalance, fromLocalTokenNetworkBalance);
          if (supportedNetwork != null) {
            fromNetwork = supportedNetwork;
            sourceNetwork = supportedNetwork.coreNetworkId;
          }

          sourceToken = localToken;
          this.fromLocalTokenNetworkBalance = fromLocalTokenNetworkBalance;
          this.fromLocalAddressTokenBalance = fromLocalAddressTokenBalance;
          transferExchangeRate = null;
          checkButtonEnabled();
          notifyListeners();

          EasyLoading.show();
          getTargetCurrency(fromAssetCode!).whenComplete(() {
            EasyLoading.dismiss();
          });

          notifyListeners();
        },
        selectedTokenNetworkBalance: fromLocalTokenNetworkBalance,
      ),
    );
  }

  void onUseMaxPressed() {
    if (sourceToken?.balance != null) {
      String maxBalance = sourceToken
              ?.balanceAsDouble()
              .currencyFormat(
                  decimalDigits: sourceToken?.decimals?.toInt() ?? 8,
                  symbol: "")
              .removeTrailingZero() ??
          '';
      fromTextEditingController.text = maxBalance;
      fromTextEditingController.selection =
          TextSelection.collapsed(offset: maxBalance.length);
      onFromInputChanged(maxBalance);
    }
  }

  back() {
    _navigationService.back();
  }

  void onContinue() async {
    if (targetFiatTransferAsset == null) {
      return;
    }

    // Check if we should show the warning
    final shouldShowWarning = locator<PreferenceService>().getBool(
          key: kShowSwapWarningForSendWithDefi,
        ) ??
        true;

    if (shouldShowWarning &&
        transferExchangeRate?.swapDetails.performSwap == true) {
      BuildContext? context = StackedService.navigatorKey?.currentContext;
      if (context != null) {
        final result = await ExternalCloseSheet.showModal(
          context,
          child: SwapWarningModalView(
            fromCurrency: sourceToken?.symbol ?? '',
            toCurrency: targetFiatTransferAsset?.symbol ?? '',
          ),
        );

        // If result is not true (user didn't click continue), return early
        if (result != true) {
          return;
        }
      }
    }

    if (transferExchangeRate!.requiresExtendedKyc == true) {
      EasyLoading.show();
      final status = await _kycService.getBridgeOnboardingStatus(
          checkUserCanBeOnboard: false);
      EasyLoading.dismiss();
      if (status != LocalKycStatus.verified) {
        _kycService.initiateKyc(kycPurpose: KycPurpose.usdAccount);
        return;
      }
    }

    if (transferExchangeRate!.requiresStandardKyc == true) {
      if (currentUser?.kycStatus != KycStatus.VERIFIED.name) {
        EasyLoading.show();
        await _userService.getUpdatedUserInfo();
        EasyLoading.dismiss();
        if (currentUser?.kycStatus != KycStatus.VERIFIED.name) {
          _kycService.initiateKyc(kycPurpose: KycPurpose.trade);
          return;
        }
      }
    }

    _navigationService.navigateToSendToBankPreviewView(
      fiatTransferAsset: targetFiatTransferAsset!,
      targetAmount: targetAmount,
      onContinue: (id) async {
        if (transferExchangeRate?.swapDetails.performSwap == true) {
          _handleSwap(id);
        } else {
          _analyticsService.logEvent(
              eventName: AnalyticsEvent.attemptSendTransaction);
          _getOrCreateAddress(id);
        }
      },
    );
  }

  @override
  String? get fromAssetCode => sourceToken?.symbol;

  @override
  void onViewModelReady() {
    super.onViewModelReady();
  }

  @override
  String get amountToBeConverted {
    if (transferExchangeRate == null) {
      return "0";
    }
    return "$fromAssetCode $amountConverted";
  }

  @override
  String get formattedFee {
    if (transferExchangeRate == null) {
      return "0";
    }
    return "$fromAssetCode $processingFee";
  }

  Widget? get fromAssetLogo {
    return sourceToken == null
        ? null
        : Stack(
            alignment: Alignment.bottomRight,
            children: [
              TokenLogoContainer(
                size: 30.78,
                imageUrl: sourceToken!.getLogoUrl() ?? '',
              ),
              CachedNetworkImage(
                height: 10,
                width: 10,
                imageUrl: fromLocalTokenNetworkBalance?.logoUrl() ?? '',
                errorWidget: (_, __, ___) => const SizedBox(),
              )
            ],
          );
  }

  @override
  void checkButtonEnabled() {
    _checkInsufficientBalance();
    hasValidInputs = insufficientBalance == false &&
        (fromAmountAsDouble ?? 0) > 0 &&
        transferExchangeRate != null;
    notifyListeners();
  }

  SendSwapParameter? sendSwapParameter;

  @override
  void setErrorMessage() {
    // TODO: implement setErrorMessage
  }

  @override
  void reset() {
    sendSwapParameter = null;
    super.reset();
  }

  bool fetchingSwapQuote = false;

  @override
  Future getSwapQuote(TransferExchangeRate transferExchangeRate) async {
    final liquidationTokens =
        transferExchangeRate.swapDetails.liquidationTokens.toList();
    if (liquidationTokens.isEmpty) {
      sendSwapParameter = null;
      fetchingSwapQuote = false;
      notifyListeners();
      return;
    }
    if (sourceToken == null ||
        fromAmountAsDouble == null ||
        fromAmountAsDouble == 0 ||
        fromAmountAsDouble == null) {
      sendSwapParameter = null;
      fetchingSwapQuote = false;
      notifyListeners();
      return;
    }
    CrossSwapQuote? crossSwapQuote;
    SwapQuote? swapQuote;
    TransferExchangeRateLiquidationToken? liquidationToken;
    fetchingSwapQuote = true;
    notifyListeners();

    for (var token in liquidationTokens) {
      if (disposed) return;
      final toNetwork = _networkService.getChainByNetworkId(token.networkId);
      if (toNetwork == null) continue;
      if (transferExchangeRate.transferFlow == TransferFlow.UNIFIED_DA_SWAP) {
        final result = await getSameNetworkSwapQuote(
          "$fromAmountAsDouble",
          fromNetwork: fromNetwork!,
          toTokenContractAddress: token.contractAddress,
        );
        if (result != null) {
          swapQuote = result;
          liquidationToken = token;
          break;
        }
      } else {
        final cross = await _getCrossChainRoute(
          fromAmountAsDouble!,
          fromNetwork: fromNetwork!,
          toNetwork: toNetwork,
          toToken: LocalToken(
            contractAddress: token.contractAddress,
            symbol: token.symbol,
            decimals: token.decimals,
            network: toNetwork,
          ),
          fromToken: sourceToken!,
        );
        if (cross != null) {
          final selectedRoute = cross!.selectedQuote;
          if (selectedRoute != null) {
            crossSwapQuote = cross;
            liquidationToken = token;
            break;
          }
        }
      }
    }
    if (disposed) return;
    if (crossSwapQuote == null && swapQuote == null) {
      fetchingSwapQuote = false;
      _toastManager.showErrorToast(text: "No route found");
      notifyListeners();
      return;
    }
    if (liquidationToken != null) {
      LocalNetwork? toNetwork =
          _networkService.getChainByNetworkId(liquidationToken.networkId);
      sendSwapParameter = SendSwapParameter(
        exchangeRate: transferExchangeRate,
        networkSymbol: sourceToken?.network?.nativeAsset ?? "",
        toNetwork: toNetwork!,
        fromNetwork: fromNetwork!,
        fromTokenAmount: swapQuote?.fromTokenAmount ??
            amountAsBigString("$fromAmountAsDouble"),
        targetFiatTransferAsset: targetFiatTransferAsset!,
        crossSwapQuote: crossSwapQuote,
        fromToken: sourceToken,
        swapQuote: swapQuote,
        toToken: LocalToken(
          contractAddress: liquidationToken.contractAddress,
          symbol: liquidationToken.symbol,
          decimals: liquidationToken.decimals,
          network: LocalNetwork(
            coreNetworkId: liquidationToken.networkId,
            networkId: liquidationToken.networkId,
            chainId: toNetwork.chainId,
          ),
        ),
      );
      this.transferExchangeRate = transferExchangeRate;
      fetchingSwapQuote = false;
      updateToTextAmount();
      notifyListeners();
      checkButtonEnabled();
    }
  }

  Future _getCrossChainRoute(
    double amount, {
    required LocalNetwork fromNetwork,
    required LocalNetwork toNetwork,
    required LocalToken fromToken,
    required LocalToken toToken,
  }) async {
    final body = SwingRouteRequestBody(
      fromChain: fromNetwork.coreNetworkId,
      fromChainId: fromNetwork.chainId.toString(),
      tokenSymbol: fromToken.symbol,
      fromTokenAddress: fromToken.contractAddress,
      fromUserAddress: locator<WalletService>().getWalletAddress,
      toChain: toNetwork.coreNetworkId,
      toChainId: toNetwork.chainId.toString(),
      toTokenSymbol: toToken.symbol,
      toTokenAddress: toToken.contractAddress,
      tokenAmount: amountAsBigString("$amount"),
      toUserAddress: locator<WalletService>().getWalletAddress,
    );
    final response =
        await _crossSwapService.getRoute(body: body, useOnlyLifi: false);
    if (!response.hasRoute) {
      return null;
    }
    return response;
  }

  Future _handleSwap(dynamic paymentMethod) async {
    String? id;
    if (paymentMethod is PaymentMethodsSvcPaymentMethod) {
      id = paymentMethod.paymentMethodId;
    } else if (paymentMethod is FiatPaymentMethod) {
      id = paymentMethod.id;
    }
    if (id == null) {
      return;
    }
    if (sendSwapParameter != null) {
      sendSwapParameter!.paymentMethodId = id;
      _navigationService.navigateToView(ConfirmSendViaSwapView(
        confirmSwapParameter: sendSwapParameter!,
      ));
    }
  }

  Future<SwapQuote?> getSameNetworkSwapQuote(String amount,
      {required LocalNetwork fromNetwork,
      required String toTokenContractAddress}) async {
    SwapQuote? swapQuote;
    try {
      String? fromAddress = sourceToken!.isCoin
          ? _swapService
              .getNativeToken(fromNetwork.coreNetworkId!)
              ?.contractAddress
          : sourceToken!.contractAddress;
      String? toAddress = toTokenContractAddress;
      var query = {
        'fromTokenAddress': fromAddress,
        'toTokenAddress': toAddress,
        'amount': amountAsBigString(amount),
      };
      final response = await _swapService.getQuote(
        query: query,
        chainId: fromNetwork.chainId!.toString(),
      );
      response.when(success: (success) {
        swapQuote = success;
      }, failure: (failure) {
        getLogger(toString()).d(failure);
      });
    } catch (e) {
      getLogger(toString()).d(e);
    }
    return swapQuote;
  }

  String amountAsBigString(String amount) {
    double withdrawAmount = double.parse(amount);
    double balance =
        withdrawAmount * pow(10, sourceToken?.decimals?.toInt() ?? 18);
    return Decimal.parse(balance.ceil().toString()).toString();
  }

  Future _getOrCreateAddress(dynamic paymentMethod) async {
    String? id;
    if (paymentMethod is PaymentMethodsSvcPaymentMethod) {
      id = paymentMethod.paymentMethodId;
    } else if (paymentMethod is FiatPaymentMethod) {
      id = paymentMethod.id;
    }
    if (id == null) {
      return;
    }
    EasyLoading.show();
    final response = await _sendService.getPaymentMethodAddress(
      paymentMethodId: id,
      source: targetFiatTransferAsset!.paymentMethodSource,
      currency: targetFiatTransferAsset!.symbol,
      networkId: sourceNetwork!,
      token: fromAssetCode!,
    );
    EasyLoading.dismiss();
    response.when(success: (success) async {
      String? address = success?.addressDetails?.address;
      if (address == null) {
        address = await _createAddress(id!, targetFiatTransferAsset!);
        if (address != null) {
          _toDirectAccountPreview(
              address: address,
              targetFiatTransferAsset: targetFiatTransferAsset!,
              paymentMethod: paymentMethod);
        }
      } else {
        _toDirectAccountPreview(
            address: address,
            targetFiatTransferAsset: targetFiatTransferAsset!,
            paymentMethod: paymentMethod);
      }
    }, failure: (failure) async {
      _toastManager.showErrorToast(text: failure.message);
    });
  }

  Future<String?> _createAddress(
      String paymentMethodId, FiatTransferAsset targetFiatTransferAsset) async {
    EasyLoading.show();
    PaymentMethodAddressRequest paymentMethodAddressRequest =
        PaymentMethodAddressRequest((b) {
      b.currency = targetFiatTransferAsset.symbol;
      b.networkId = sourceNetwork!;
      b.source_ = targetFiatTransferAsset.paymentMethodSource;
      b.token = fromAssetCode!;
    });
    String? address;
    final response = await _sendService.createPaymentMethodAddress(
      paymentMethodId: paymentMethodId,
      paymentMethodAddressRequest: paymentMethodAddressRequest,
    );
    EasyLoading.dismiss();
    response.when(success: (success) {
      address = success?.address;
    }, failure: (failure) {
      _toastManager.showErrorToast(text: failure.message);
    });
    EasyLoading.dismiss();
    return address;
  }

  Future _toDirectAccountPreview(
      {required String address,
      required FiatTransferAsset targetFiatTransferAsset,
      dynamic paymentMethod}) async {
    if (transferExchangeRate!.swapDetails.performSwap == true) {
      _toastManager.showErrorToast(text: "Swap required");
      return;
    }
    final rawString = fromTextEditingController.text
        .removeCommas()
        .removeSymbol(fromAssetCode ?? "");
    final sendAssetModel = SendAssetModel(
      fromAddress: locator<WalletService>().getWalletAddress!,
      tokenNetwork: fromLocalTokenNetworkBalance!,
      from: SpotWallet(),
      token: fromLocalAddressTokenBalance!,
      amount: rawString,
      toAddress: address,
    );
    if (paymentMethod is PaymentMethodsSvcPaymentMethod) {
      _toDirectAccountWithPaymentMethodsSvcPaymentMethod(
        sendAssetModel: sendAssetModel,
        paymentMethod: paymentMethod,
        targetFiatTransferAsset: targetFiatTransferAsset,
      );
    } else if (paymentMethod is FiatPaymentMethod) {
      _toDirectAccountWithFiatPaymentMethod(
        sendAssetModel: sendAssetModel,
        paymentMethod: paymentMethod,
        targetFiatTransferAsset: targetFiatTransferAsset,
      );
    }
  }

  _toDirectAccountWithPaymentMethodsSvcPaymentMethod({
    required SendAssetModel sendAssetModel,
    required PaymentMethodsSvcPaymentMethod paymentMethod,
    required FiatTransferAsset targetFiatTransferAsset,
  }) {
    final bank = paymentMethod.getBank();
    _navigationService.navigateToDirectAccountTransferPreviewView(
      transferModel: sendAssetModel,
      currency: targetFiatTransferAsset.symbol,
      maskedAccountNumber: bank.maskedAccountNumber,
      bankName: bank.bankName ?? "",
    );
  }

  _toDirectAccountWithFiatPaymentMethod(
      {required SendAssetModel sendAssetModel,
      required FiatPaymentMethod paymentMethod,
      required FiatTransferAsset targetFiatTransferAsset}) {
    _navigationService.navigateToDirectAccountTransferPreviewView(
      transferModel: sendAssetModel,
      currency: targetFiatTransferAsset.symbol,
      maskedAccountNumber: paymentMethod.accountNumber,
      bankName: paymentMethod.bankName,
    );
  }
}
