import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_core_crypto_public_api/nestcoinco_core_crypto_public_api.dart';
import 'package:onboard_wallet/api/onboard_exception.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/models/card/local_card_account/index.dart';
import 'package:onboard_wallet/models/cross_swap/li_fi_quote_response/li_fi_quote_response.dart';
import 'package:onboard_wallet/models/local/transaction/transaction_direction.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/models/swing/cross_swap_quote/swing_quote.dart';
import 'package:onboard_wallet/models/swing/swing.dart';
import 'package:onboard_wallet/models/token/wallet_type.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/dialogs/info_alert/external_close_dialog.dart';
import 'package:onboard_wallet/ui/views/cards/card_terminated/widgets/card_termination_dialog.dart';
import 'package:onboard_wallet/ui/views/fund_options/fund_options_viewmodel.dart';
import 'package:onboard_wallet/ui/views/swap/confirm_swap/widgets/slippage_modal.dart';
import 'package:onboard_wallet/ui/widgets/bottomsheet/bottomsheet.dart';
import 'package:onboard_wallet/ui/widgets/buttons/gas_nudge_buttons.dart';
import 'package:onboard_wallet/utils/utils.dart';
import 'package:nestcoinco_core_crypto_public_api/nestcoinco_core_crypto_public_api.dart'
    hide TransactionReceipt;
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:web3dart/web3dart.dart' as web3;

import '../../../../constants/constants.dart';

class ConfirmSwapViewModel extends BaseViewModel {
  final _cryptoService = locator<CryptoService>();
  final _web3Service = locator<Web3Service>();
  final _navigationService = locator<NavigationService>();
  final _networkService = locator<NetworkService>();
  final _toastManager = locator<ToastManager>();
  final _analyticsService = locator<AnalyticsService>();
  final _crossSwapService = locator<CrossSwapService>();
  final _cardService = locator<CardService>();
  final _virtualAccountService = locator<VirtualAccountService>();

  final _nestcoinRpcDatabaseService = locator<NestcoinRpcDatabaseService>();

  SwapQuote? get swapQuote => confirmSwapParameter.swapQuote;

  CrossSwapQuote? get crossSwapQuote => confirmSwapParameter.crossSwapQuote;

  String get networkSymbol => confirmSwapParameter.networkSymbol;
  final _swapService = locator<SwapService>();

  LocalToken? get fromToken => confirmSwapParameter.fromToken;

  LocalToken? get toToken => confirmSwapParameter.toToken;
  final ConfirmSwapParameter confirmSwapParameter;
  final CardFundingParameter? cardFundingParameter;
  final VirtualAccountFundingParameter? virtualAccountFundingParameter;
  LocalAddressTokenBalance? nativeToken;

  bool get isCardFunding => cardFundingParameter != null;
  bool get isVirtualAccountFunding => virtualAccountFundingParameter != null;

  String get myWalletAddress => locator<WalletService>().getWalletAddress ?? "";

  num _maxSlippage = 1.0;

  num get maxSlippage => _maxSlippage;

  set maxSlippage(num slippage) {
    _maxSlippage = slippage;
    notifyListeners();
  }

  String? rpcUrl;
  String? gasFee;
  BigInt? feeAsBigInt;
  bool? requiresApproval;
  bool showApprovalFlowWidget = false;
  bool isApproving = false;
  bool isSwapping = false;

  String get walletAddress =>
      myWalletAddress.middleOverflow(prefixCharacter: 9, suffixCharacter: 10);

  bool get isCurrentNetworkSupportedByExchange =>
      _networkService.isCurrentNetworkSupportedByExchange;

  bool? insufficientFeeBalance;

  String? get feeFiatValue => _feeFiatValue();

  String buttonText() {
    if (isApproving == true) {
      return "${S.current.approving} ${fromToken?.symbol ?? ""}";
    }
    if (isSwapping) {
      return "${S.current.swapping} ${fromToken?.symbol ?? ""}";
    }
    if (requiresApproval == true) {
      return S.current.approve;
    }
    return S.current.swap;
  }

  String get feeWithSymbol => "${gasFee ?? ''} ${networkSymbol.toUpperCase()}";

  String get networkAlias =>
      locator<NetworkService>().currentChain?.symbol ?? '';

  String get rate => _getRate() ?? '';

  String get fromAmount => _fromAmount();

  String get toAmount => _toAmount();

  String get fromSymbol => confirmSwapParameter.fromSymbol;

  String get toSymbol => confirmSwapParameter.toSymbol;

  String get fromLogoUrl => confirmSwapParameter.fromLogoUrl;

  String get toLogoUrl => confirmSwapParameter.toLogoUrl;

  String get fromAddress => confirmSwapParameter.fromAddress;

  String get toAddress => confirmSwapParameter.toAddress;

  LocalNetwork get fromNetwork => confirmSwapParameter.fromNetwork;

  LocalNetwork get toNetwork => confirmSwapParameter.toNetwork;

  Widget get providerImage =>
      swapQuote == null ? crossSwapQuote!.image : Assets.svg.oneInchLogo.svg();

  String get providerName =>
      swapQuote == null ? crossSwapQuote!.providerName : "1inch";

  bool _fetchingFee = true;

  bool get fetchingFee => _fetchingFee;

  set fetchingFee(bool fetchingFee) {
    _fetchingFee = fetchingFee;
    notifyListeners();
  }

  ConfirmSwapViewModel(
      {required this.confirmSwapParameter,
      this.cardFundingParameter,
      this.virtualAccountFundingParameter});

  onModelReady() {
    _setup();
    _setRefreshQuoteTimer();
    _initSlippage();
  }

  Future _setup() async {
    setBusy(true);
    if (fromNetwork.chainId == null) {
      EasyLoading.showToast("Failed to fetch network id");
      return;
    }
    var nestcoinRpc = await _nestcoinRpcDatabaseService.getRpc(
        chainId: fromNetwork.chainId!.toInt());
    if (nestcoinRpc != null) {
      rpcUrl = nestcoinRpc.rpc;
    }
    if (rpcUrl == null) {
      EasyLoading.show();
      rpcUrl =
          await _cryptoService.getRpc(networkId: fromNetwork.coreNetworkId!);
      EasyLoading.dismiss();
      if (rpcUrl == null) {
        EasyLoading.showToast("Failed to fetch rpc url");
        return;
      }
    }
    if (rpcUrl != null) {
      EasyLoading.dismiss();
      await _fetchFee();
      await _checkAllowance();
    }
    setBusy(false);
  }

  Future<void> _fetchFee() async {
    if (swapQuote != null) {
      fetchingFee = true;
      web3.EtherAmount? gasPrice =
          await _web3Service.getGasPrice(rpcUrl: rpcUrl!);
      final gasEstimateWei =
          BigInt.from(swapQuote!.estimatedGas) * gasPrice.getInWei;
      feeAsBigInt = gasEstimateWei;
    } else {
      final totalFee = crossSwapQuote?.getTotalFee;
      feeAsBigInt = BigInt.from(totalFee ?? 0);
    }
    fetchingFee = false;
    if (feeAsBigInt != null) {
      final gasEstimateInCoin =
          feeAsBigInt!.toDouble() / (1000000000 * 1000000000);
      gasFee = gasEstimateInCoin
          .currencyFormat(decimalDigits: 4, symbol: "")
          .removeTrailingZero();
      insufficientFeeBalance = await _checkBalance();
    }
    notifyListeners();
  }

  Future _checkAllowance() async {
    if (swapQuote != null) {
      final result = await _swapService.isApprovalRequired(
        tokenAddress: swapQuote!.fromToken.address,
        walletAddress: myWalletAddress,
        amount: swapQuote!.fromTokenAmount,
        chainId: fromNetwork.chainId.toString(),
      );
      result.when(
        success: (isRequired) {
          requiresApproval = isRequired;
          showApprovalFlowWidget = isRequired;
          notifyListeners();
        },
        failure: (failure) {
          EasyLoading.showError(failure.message);
        },
      );
    } else if (crossSwapQuote != null) {
      if (!crossSwapQuote!.hasRoute) return;
      final response = await _crossSwapService.isApprovalRequired(
        crossSwapQuote: crossSwapQuote!,
        userAddress: myWalletAddress,
        amount: confirmSwapParameter.fromTokenAmount,
        rpcUrl: rpcUrl!,
      );
      response.when(success: (isRequired) {
        requiresApproval = isRequired;
        showApprovalFlowWidget = isRequired;
        notifyListeners();
      }, failure: (failure) {
        EasyLoading.showError(failure.message);
      });
    }
  }

  Future _monitorTokenApprovalTransaction(String hash) async {
    if (disposed) return;
    web3.TransactionReceipt? transactionReceipt =
        await _web3Service.getTransactionReceipt(rpcUrl: rpcUrl!, hash: hash);
    if (transactionReceipt == null) {
      await Future.delayed(const Duration(seconds: 5));
      await _monitorTokenApprovalTransaction(hash);
      return;
    }
    if (transactionReceipt.status == true) {
      isApproving = false;
      isSwapping = false;
      notifyListeners();
      requiresApproval = false;
      _toastManager.showToast(text: S.current.tokenApprovedCompleteYourSwap);
    } else if (transactionReceipt.blockNumber.isPending == true) {
      await Future.delayed(const Duration(seconds: 5));
      await _monitorTokenApprovalTransaction(hash);
    } else if (transactionReceipt.status == false) {
      _toastManager.showErrorToast(text: S.current.transactionFailed);
      requiresApproval = true;
      isApproving = false;
      isSwapping = false;
      notifyListeners();
    }
  }

  Future _swapToken() async {
    try {
      isSwapping = true;
      _refreshTimer?.cancel();
      notifyListeners();

      if (swapQuote != null) {
        final response = await _swapService.buildTxForSwap(
          chainId: fromNetwork.chainId.toString(),
          walletAddress: myWalletAddress,
          fromTokenAddress: swapQuote!.fromToken.address,
          toTokenAddress: swapQuote!.toToken.address,
          amount: swapQuote!.fromTokenAmount,
          slippage: maxSlippage,
          receiverWalletAddress: confirmSwapParameter.receiverWalletAddress,
          fee: confirmSwapParameter.feeAsPercentage,
          referrerAddress: confirmSwapParameter.partnerAddress,
        );
        response.when(
          success: (json) async {
            final transaction = await _createTransactionWithOptimalGas(
              data: json[EthProviderConstants.data],
              to: json[EthProviderConstants.to],
              from: json[EthProviderConstants.from],
              value: json[EthProviderConstants.value],
              fallbackGasPrice: json[EthProviderConstants.gasPrice],
            );
            await _performSwapTransaction(transaction);
          },
          failure: (failure) {
            isSwapping = false;
            _showErrorMessage(failure);
            notifyListeners();
          },
        );
      } else if (crossSwapQuote != null) {
        web3.EtherAmount? gasPrice =
            await _web3Service.getGasPrice(rpcUrl: rpcUrl!);
        num? feeAsSwingBasePoints;
        if (confirmSwapParameter.feeAsPercentage != null) {
          feeAsSwingBasePoints = _crossSwapService
              .getSwingFeeBasePoints(confirmSwapParameter.feeAsPercentage!);
        }

        if (crossSwapQuote!.selectedQuote is SwingQuote) {
          final response = await _crossSwapService.send(
            quote: (crossSwapQuote!.selectedQuote as SwingQuote),
            providerRoute: crossSwapQuote!.swingProviderRoute!,
            userAddress: myWalletAddress,
            tokenAmount: confirmSwapParameter.fromTokenAmount,
            toUserAddress: confirmSwapParameter.receiverWalletAddress,
            fee: feeAsSwingBasePoints,
            partnerAddress: confirmSwapParameter.partnerAddress,
          );
          response.when(success: (result) async {
            final transaction = await _createTransactionWithOptimalGas(
              data: result.tx?.data,
              to: result.tx?.to,
              from: result.tx?.from,
              value: result.tx?.value,
              fallbackGasPrice: gasPrice.getInWei.toString(),
            );
            await _performSwapTransaction(transaction,
                txId: result.id?.toString());
          }, failure: (failure) {
            isSwapping = false;
            _showErrorMessage(failure);
            notifyListeners();
          });
        } else if (crossSwapQuote!.selectedQuote is LiFiQuoteResponse) {
          LiFiQuoteResponse quote =
              crossSwapQuote!.selectedQuote as LiFiQuoteResponse;
          final result = quote.transactionRequest;
          final transaction = await _createTransactionWithOptimalGas(
            data: result?.data,
            to: result?.to,
            from: result?.from,
            value: result?.value,
            fallbackGasPrice: gasPrice.getInWei.toString(),
            gasLimit: result?.gasLimit,
          );
          await _performSwapTransaction(transaction, isLiFi: true);
        }
      }
    } on Exception catch (e) {
      isSwapping = false;
      _showErrorMessage(e);
      notifyListeners();
    }
  }

  Future _getApprovalTransaction() async {
    isApproving = true;
    notifyListeners();
    web3.Transaction? transaction;

    if (swapQuote != null) {
      final result = await _swapService.buildTxForApproveTradeWithRouter(
        tokenAddress: swapQuote!.fromToken.address,
        amount: swapQuote!.fromTokenAmount,
        chainId: fromNetwork.chainId.toString(),
      );
      result.when(
        success: (json) async {
          transaction = await _createTransactionWithOptimalGas(
            data: json[EthProviderConstants.data],
            to: json[EthProviderConstants.to],
            from: myWalletAddress,
            value: json[EthProviderConstants.value],
            fallbackGasPrice: json[EthProviderConstants.gasPrice],
          );
        },
        failure: (failure) {
          isApproving = false;
          EasyLoading.showError(failure.message);
          notifyListeners();
        },
      );
    } else if (crossSwapQuote != null) {
      final response = await _crossSwapService.getApprovalCallData(
        crossSwapQuote: crossSwapQuote!,
        userAddress: myWalletAddress,
        tokenAmount: confirmSwapParameter.fromTokenAmount,
      );
      response.when(success: (result) {
        transaction = result;
      }, failure: (failure) {
        isApproving = false;
        EasyLoading.showError(failure.message);
        notifyListeners();
      });
    }
    isApproving = false;
    notifyListeners();
    //TODO: Figure out why navigateToPasscodeAndBiometricView doesn't work
    // When called from Modal
    _approveTransaction(transaction!);
    // if (transaction != null) {
    //   BuildContext? context = StackedService.navigatorKey?.currentContext;
    //   if (context != null) {
    //     ExternalCloseSheet.showModal(
    //       context,
    //       child: ApproveSwapView(
    //         transaction: transaction!,
    //         walletAddress: myWalletAddress,
    //         approve: () {

    //         },
    //         fromTokenSymbol: fromToken?.symbol ?? "",
    //         rpcUrl: rpcUrl!,
    //         nativeSymbol: fromNetwork.nativeAsset ??
    //             fromNetwork.nativeCurrency?.name ??
    //             "",
    //       ),
    //     );
    //   }
    // }
  }

  Future _approveTransaction(web3.Transaction transaction) async {
    isApproving = true;
    notifyListeners();
    try {
      final privateKey = await locator<WalletService>().getPrivateKey();
      if (privateKey == null) return;
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.signsSwapSignatureRequest);
      int? transactionCount = await _web3Service.getTransactionCount(
        address: myWalletAddress,
        atBlock: 'pending',
        rpcUrl: rpcUrl!,
      );
      final trxHash = await _web3Service.sendTransaction(
        rpcUrl: rpcUrl!,
        transaction: transaction.copyWith(nonce: transactionCount),
        privateKey: privateKey,
        fetchChainIdFromNetworkId: true,
      );
      if (trxHash != null) {
        await _monitorTokenApprovalTransaction(trxHash);
      } else {
        isApproving = false;
        EasyLoading.showError(S.current.transactionFailed);
        notifyListeners();
      }
    } on Exception catch (e) {
      isApproving = false;
      notifyListeners();
      _showErrorMessage(e);
    }
  }

  Future<String?> _performSwapTransaction(web3.Transaction transaction,
      {String? txId, bool isLiFi = false}) async {
    possibleSlippageError = false;
    notifyListeners();
    String? trxHash;
    try {
      String? privateKey = await locator<WalletService>().getPrivateKey();
      if (privateKey == null) return null;
      int? transactionCount = await _web3Service.getTransactionCount(
        address: myWalletAddress,
        atBlock: 'pending',
        rpcUrl: rpcUrl!,
      );
      trxHash = await _web3Service.sendTransaction(
        rpcUrl: rpcUrl!,
        transaction: transaction.copyWith(nonce: transactionCount),
        privateKey: privateKey,
        fetchChainIdFromNetworkId: true,
      );
      if (trxHash != null) {
        bool isCrossSwap = txId != null || isLiFi;
        _logTransactionEvent(isCrossSwap);
        if (isCrossSwap) {
          // Crossswap
          locator<CrossSwapService>().startPolling(
              txId: txId ?? trxHash, txHash: trxHash, isLifi: isLiFi);
        } else {
          LocalTransaction localTransaction = LocalTransaction(
            to: transaction.to?.hex,
            value: transaction.value?.getInWei.toString(),
            from: transaction.from?.hex,
            hash: trxHash,
            gasFees: feeAsBigInt.toString(),
            timestamp: DateTime.now().millisecondsSinceEpoch,
            status: LocalTransactionStatus.pending,
            contractAddress: fromAddress,
            networkId: fromNetwork.coreNetworkId,
            direction: TransactionDirection.OUT,
          );
          locator<TransactionHashMonitoringService>().observeTransactionHash(
            localTransaction: localTransaction,
            rpcUrl: rpcUrl!,
            tokenSymbol: fromSymbol,
            cryptoTransactionType: OnboardCryptoTransactionType.swap,
          );
        }

        if (isCardFunding || isVirtualAccountFunding) {
          _toSuccessView();
        } else {
          _navigationService.navigateToTransactionSuccessView(
            title: S.current.transactionSentTitle,
            onButtonTapped: () {
              _analyticsService.logEvent(
                  eventName: AnalyticsEvent.swapSuccessful);
              _navigationService.back();
              _navigationService.back();
              _navigationService.back();
            },
          );
        }
      } else {
        isSwapping = false;
        isApproving = false;
        EasyLoading.showError(S.current.transactionFailed);
        notifyListeners();
      }
    } on Exception catch (e) {
      isSwapping = false;
      isApproving = false;
      notifyListeners();
      _showErrorMessage(e);
    }
    return trxHash;
  }

  /// Creates a transaction with optimal gas parameters for the current network
  /// Uses EIP-1559 for all networks, falls back to legacy gas pricing if EIP-1559 fails
  Future<web3.Transaction> _createTransactionWithOptimalGas({
    required String? data,
    required String? to,
    required String? from,
    required String? value,
    String? fallbackGasPrice,
    String? gasLimit,
  }) async {
    final networkId = fromNetwork.coreNetworkId;

    // Try to get EIP-1559 gas estimates for all networks
    if (networkId != null) {
      try {
        final transactionValue = value != null && value.isNotEmpty
            ? BigInt.tryParse(value)?.toRadixString(16)
            : "0x0";

        final gasEstimateResponse = await _cryptoService.getGasEstimates(
          networkId: networkId,
          gasEstimateRequest: GasEstimateRequest((b) {
            b.from = from;
            b.to = to;
            b.data = data ?? "0x";
            b.decode = data != null && data.isNotEmpty;
            b.value = transactionValue != null ? '0x$transactionValue' : "0x0";
          }),
        );

        return gasEstimateResponse.when(
          success: (gasData) async {
            // Extract EIP-1559 parameters
            final gasLimitBigInt = gasLimit != null
                ? BigInt.tryParse(gasLimit)
                : (gasData?.gasLimit != null
                    ? BigInt.tryParse(gasData?.gasLimit ?? "")
                    : null);

            final maxFeePerGas = gasData?.feeData.maxFeePerGas;
            final maxPriorityFeePerGas = gasData?.feeData.maxPriorityFeePerGas;

            final maxFeePerGasAmount =
                maxFeePerGas != null ? getEtherAmount(maxFeePerGas) : null;
            final maxPriorityFeePerGasAmount = maxPriorityFeePerGas != null
                ? getEtherAmount(maxPriorityFeePerGas)
                : null;

            // Create EIP-1559 transaction if we have the required parameters
            if (maxFeePerGasAmount != null &&
                maxPriorityFeePerGasAmount != null) {
              return web3.Transaction(
                data: getData(data),
                to: getAddress(to),
                from: getAddress(from),
                maxGas: gasLimitBigInt?.toInt(),
                maxFeePerGas: maxFeePerGasAmount,
                maxPriorityFeePerGas: maxPriorityFeePerGasAmount,
                value: getEtherAmount(value),
              );
            } else {
              // Fallback to legacy transaction
              return _createLegacyTransaction(
                data: data,
                to: to,
                from: from,
                value: value,
                fallbackGasPrice: fallbackGasPrice,
                gasLimit: gasLimitBigInt,
              );
            }
          },
          failure: (failure) async {
            // Fallback to legacy transaction on API failure
            return _createLegacyTransaction(
              data: data,
              to: to,
              from: from,
              value: value,
              fallbackGasPrice: fallbackGasPrice,
              gasLimit: gasLimit != null ? BigInt.tryParse(gasLimit) : null,
            );
          },
        );
      } catch (e) {
        // Fallback to legacy transaction on any error
        getLogger(toString()).w(
            "Failed to get EIP-1559 gas estimates, falling back to legacy: $e");
      }
    }

    // When EIP-1559 fails or networkId is null, use legacy transaction
    return _createLegacyTransaction(
      data: data,
      to: to,
      from: from,
      value: value,
      fallbackGasPrice: fallbackGasPrice,
      gasLimit: gasLimit != null ? BigInt.tryParse(gasLimit) : null,
    );
  }

  /// Creates a legacy transaction with gasPrice
  web3.Transaction _createLegacyTransaction({
    required String? data,
    required String? to,
    required String? from,
    required String? value,
    String? fallbackGasPrice,
    BigInt? gasLimit,
  }) {
    return web3.Transaction(
      data: getData(data),
      to: getAddress(to),
      from: getAddress(from),
      gasPrice: getEtherAmount(fallbackGasPrice),
      maxGas: gasLimit?.toInt(),
      value: getEtherAmount(value),
    );
  }

  Future buttonTapped() async {
    if (isApproving || isSwapping) return;
    if (insufficientFeeBalance == true) {
      BuildContext? context = StackedService.navigatorKey?.currentContext;
      if (context == null) return;
      ExternalCloseSheet.showModal(
        context,
        child: GasNudgeBottomSheet(
          onBuyPressed: () => onBuyGasPressed(),
          onDepositPressed: () => onDepositGasPressed(),
          fiatValueOfGas: feeFiatValue,
          nativeToken: nativeToken,
        ),
      );
      return;
    }
    _analyticsService.logEvent(eventName: AnalyticsEvent.confirmsSwap);
    if (requiresApproval == true) {
      _getApprovalTransaction();
    } else {
      await _swapToken();
    }
  }

  String? _getRate() {
    final numFromAmount = num.tryParse(fromAmount);
    final numToAmount = num.tryParse(toAmount);

    if (numFromAmount != null && numToAmount != null) {
      return (numToAmount / numFromAmount)
          .currencyFormat(decimalDigits: 4, symbol: "")
          .removeTrailingZero();
    }
    return null;
  }

  Future<bool?> _checkBalance() async {
    final tokenService = locator<TokenService>();
    String? coreNetworkId = fromNetwork.coreNetworkId;
    nativeToken = await tokenService.getNativeToken(coreNetworkId);
    final tokenNetworkBalance =
        nativeToken?.networks?.firstWhereOrNull((element) {
      return element.id?.toLowerCase() == coreNetworkId?.toLowerCase();
    });
    if (tokenNetworkBalance == null) return null;
    double availableBalance =
        double.tryParse(tokenNetworkBalance.balance ?? '') ?? 0;
    double? transactionValue = double.tryParse(gasFee ?? '');

    String? address =
        _swapService.getNativeToken(coreNetworkId!)?.contractAddress ??
            tokenNetworkBalance.contractAddress;

    if (fromAddress == address &&
        tokenNetworkBalance.id?.toLowerCase() ==
            fromNetwork.networkId?.toLowerCase()) {
      if (transactionValue != null) {
        transactionValue += double.tryParse(fromAmount) ?? 0;
      } else {
        transactionValue = double.tryParse(fromAmount);
      }
    }
    if (transactionValue != null) {
      return transactionValue > availableBalance;
    }
    return null;
  }

  onBuyGasPressed() {
    if (nativeToken == null) {
      _navigationService.navigateToSelectAssetsView();
    } else {
      final tokenNetworkBalance = nativeToken?.networks?.firstWhere(
        (element) =>
            element.id?.toLowerCase() ==
            fromNetwork.coreNetworkId?.toLowerCase(),
      );
      locator<WalletNavigationService>().navigateToFundingOption(
        tokenBalance: nativeToken!,
        walletType: WalletType.spot,
        fundOptionType: FundOptionFLowType.add,
        tokenNetworkBalance: tokenNetworkBalance,
      );
    }
  }

  _logTransactionEvent(bool isCrossSwap) {
    final analytics = locator<AnalyticsService>();
    analytics.logEvent(
      eventName: AnalyticsEvent.swap,
      parameters: {
        EventParameterKey.from: fromSymbol,
        EventParameterKey.to: toSymbol,
        EventParameterKey.amount: fromAmount,
        "is_cross_swap": isCrossSwap.toString(),
        "network_from": fromNetwork.name,
        "network_to": toNetwork.name,
      },
    );
  }

  String _toAmount() {
    return (double.parse(confirmSwapParameter.toAmount) /
            pow(10, toToken?.decimals ?? 18))
        .currencyFormat(
            decimalDigits: toToken?.decimals?.toInt() ?? 18, symbol: "")
        .removeTrailingZero();
  }

  String _fromAmount() {
    return (double.parse(confirmSwapParameter.fromTokenAmount) /
            pow(10, fromToken?.decimals ?? 18))
        .currencyFormat(
            decimalDigits: fromToken?.decimals?.toInt() ?? 18, symbol: "")
        .removeTrailingZero();
  }

  String? _feeFiatValue() {
    double? price = nativeToken?.price();
    num? feeAsNum = num.tryParse(gasFee ?? '');

    if (price == null || feeAsNum == null) return null;

    return (feeAsNum * price).currencyFormat();
  }

  bool possibleSlippageError = false;

  void _showErrorMessage(Exception e) {
    BuildContext? context = StackedService.navigatorKey?.currentContext;

    if (e is OnboardExceptions) {
      if (context == null) {
        _toastManager.showErrorToast(
          text: e.message,
        );
      } else {
        ExternalCloseDialog.showExternalDialog(context,
            child: OnbwErrorDialog(message: e.message));
      }
    } else {
      if (e.toString().toLowerCase().contains("execution reverted")) {
        possibleSlippageError = true;
        notifyListeners();
        _toastManager.showErrorToast(
          text: S.current.thisSwapFailedTryIncreasingYourSlippage,
        );
      } else if (e.toString().contains("insufficient funds")) {
        final symbol = nativeToken?.symbol ?? "";
        final message = S.current.insufficientBalanceSwapCopy(symbol);

        insufficientFeeBalance = true;
        if (context == null) {
          _toastManager.showErrorToast(text: message);
        } else {
          ExternalCloseDialog.showExternalDialog(
            context,
            child: OnbwErrorDialog(message: message),
          );
        }
      } else if (e.toString().isDigit) {
        if (context == null) {
          _toastManager.showErrorToast(text: S.current.swapGenericErrorMessage);
        } else {
          ExternalCloseDialog.showExternalDialog(context,
              child:
                  OnbwErrorDialog(message: S.current.swapGenericErrorMessage));
        }
      } else if (e is SocketException) {
        _toastManager.showErrorToast(
            text: S.current.poorInternetConnectionPleaseTryAgain);
      } else {
        _toastManager.showErrorToast(text: e.toString());
      }
    }
  }

  void onDepositGasPressed() {
    final tokenNetworkBalance = nativeToken?.networks?.firstWhere(
      (element) =>
          element.id?.toLowerCase() == fromNetwork.coreNetworkId?.toLowerCase(),
    );
    _navigationService.navigateToDepositView(
      walletType: nativeToken?.walletType ?? WalletType.spot,
      tokenNetworkBalance: tokenNetworkBalance,
      token: nativeToken,
    );
  }

  _toSuccessView() {
    if (isCardFunding) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.toConfirmCardPurchaseSuccessScreen,
          parameters: {
            EventParameterKey.cardFundingType:
                cardFundingParameter!.cardFundingType,
          });

      if (cardFundingParameter!.isCardCreation) {
        _analyticsService.logEvent(eventName: AnalyticsEvent.cardAttempt);
        _toCardCreationSuccessView();
      } else {
        _toCardTopUpSuccessView();
      }
    } else if (isVirtualAccountFunding) {
      _toCardTopUpSuccessView();
    }
  }

  _toCardCreationSuccessView() {
    _navigationService.navigateToTransactionSuccessView(
      title: S.current.cardRequestSuccessful,
      subtitle: S.current.cardRequestSuccessSubCopy,
      onButtonTapped: () async {
        final updatedCardAccount = cardFundingParameter!.cardAccount
          ..localCardAccountStatus = LocalCardAccountStatus.funded;
        await _cardService.cardAccountBox.put(
          updatedCardAccount.id,
          updatedCardAccount,
        );
        _navigationService
            .popUntil((route) => route.settings.name == Routes.homeView);
      },
    );
  }

  _toCardTopUpSuccessView() {
    _navigationService.navigateToTransactionSuccessView(
      title: S.current.transactionSentTitle,
      subtitle: isVirtualAccountFunding
          ? S.current.fundingVirtualWithXAmount("$fromAmount $fromSymbol")
          : S.current.fundingCardWithXAmount("$fromAmount $fromSymbol"),
      onButtonTapped: () async {
        if (isCardFunding) {
          String? accountId = cardFundingParameter?.cardAccount.id;
          if (accountId != null) {
            _cardService.getCardAccountById(accountId: accountId);
          }
        } else {
          String? accountId = virtualAccountFundingParameter?.virtualAccount.id;
          if (accountId != null) {
            _virtualAccountService.getVirtualAccountById(accountId: accountId);
          }
        }
        _navigationService
            .popUntil((route) => route.settings.name == Routes.homeView);
      },
    );
  }

  bool _refreshingRate = false;

  bool get refreshingRate => _refreshingRate;

  set refreshingRate(bool refreshingRate) {
    _refreshingRate = refreshingRate;
    notifyListeners();
  }

  Timer? _refreshTimer;

  _setRefreshQuoteTimer() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 60), (_) {
      _refreshRate();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  Future<void> _refreshRate() async {
    refreshingRate = true;
    if (confirmSwapParameter.swapQuote != null) {
      await _refreshSwapQuote();
    } else if (confirmSwapParameter.crossSwapQuote != null) {
      await _refreshCrossSwapQuote();
    }
    refreshingRate = false;
    _fetchFee();
  }

  Future<void> _refreshSwapQuote() async {
    num? chainId = confirmSwapParameter.fromNetwork.chainId;
    if (chainId != null) {
      var query = {
        'fromTokenAddress': fromAddress,
        'toTokenAddress': toAddress,
        'amount': amountAsBigString(fromAmount),
      };
      if (confirmSwapParameter.feeAsPercentage != null) {
        query['fee'] = confirmSwapParameter.feeAsPercentage.toString();
      }

      final response = await _swapService.getQuote(
        query: query,
        chainId: chainId.toString(),
      );
      response.when(success: (success) {
        confirmSwapParameter.swapQuote = success;
        notifyListeners();
      }, failure: (failure) {
        getLogger(toString()).e(failure);
      });
    }
  }

  Future<void> _refreshCrossSwapQuote() async {
    final fee = confirmSwapParameter.feeAsPercentage;
    num? feeAsSwingBasePoints;
    if (fee != null) {
      feeAsSwingBasePoints = _crossSwapService.getSwingFeeBasePoints(fee);
    }
    final body = SwingRouteRequestBody(
      fromChain: fromNetwork.coreNetworkId,
      fromChainId: fromNetwork.chainId.toString(),
      tokenSymbol: fromToken?.symbol,
      fromTokenAddress: fromToken?.contractAddress,
      fromUserAddress: myWalletAddress,
      toChain: toNetwork.coreNetworkId,
      toChainId: toNetwork.chainId.toString(),
      toTokenSymbol: toToken?.symbol,
      toTokenAddress: toToken?.contractAddress,
      tokenAmount: amountAsBigString(fromAmount),
      fee: feeAsSwingBasePoints,
      toUserAddress: myWalletAddress,
    );
    var response = await _crossSwapService.getRoute(
        body: body, selected: crossSwapQuote?.selectedQuote);
    response = response.copyWith(selectedQuote: crossSwapQuote?.selectedQuote);
    confirmSwapParameter.crossSwapQuote = response;
  }

  String amountAsBigString(String amount) {
    double withdrawAmount = double.parse(amount);
    double balance =
        withdrawAmount * pow(10, fromToken?.decimals?.toInt() ?? 18);
    return Decimal.parse(balance.toString()).toString();
  }

  void showSlippageModal() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(context,
        child: SetSlippageModal(
          slippage: maxSlippage,
          onSetSlippage: (slippageText) {
            _navigationService.back();
            final bareSlippage = slippageText.removeSymbol("%");
            maxSlippage = num.tryParse(bareSlippage) ?? maxSlippage;
            notifyListeners();
            _swapService.saveSlippage(maxSlippage);
          },
        ));
  }

  Future<void> _initSlippage() async {
    maxSlippage = _swapService.getSlippage() ?? maxSlippage;
  }
}

class VirtualAccountFundingParameter {
  final LocalVirtualAccount virtualAccount;

  VirtualAccountFundingParameter({
    required this.virtualAccount,
  });
}

class CardFundingParameter {
  final LocalCardAccount cardAccount;
  final bool isCardCreation;
  final String cardFundingType;

  CardFundingParameter({
    required this.cardAccount,
    required this.isCardCreation,
    required this.cardFundingType,
  });
}

class ConfirmSwapParameter {
  SwapQuote? swapQuote;
  CrossSwapQuote? crossSwapQuote;
  final String networkSymbol;
  final LocalToken? fromToken;
  final LocalToken? toToken;
  final LocalNetwork toNetwork;
  final LocalNetwork fromNetwork;
  final String fromTokenAmount;
  final String receiverWalletAddress;
  final num? feeAsPercentage;
  final String? partnerAddress;

  ConfirmSwapParameter({
    this.swapQuote,
    this.crossSwapQuote,
    required this.networkSymbol,
    this.fromToken,
    this.toToken,
    this.feeAsPercentage,
    this.partnerAddress,
    required this.toNetwork,
    required this.fromNetwork,
    required this.fromTokenAmount,
    required this.receiverWalletAddress,
  });

  String get toAmount {
    if (crossSwapQuote != null) {
      return crossSwapQuote!.toAmount;
    } else {
      return swapQuote!.toTokenAmount;
    }
  }

  String get fromSymbol =>
      crossSwapQuote?.fromSymbol ??
      swapQuote?.fromToken.symbol ??
      fromToken?.symbol ??
      "";

  String get toSymbol =>
      crossSwapQuote?.toSymbol ??
      swapQuote?.toToken.symbol ??
      toToken?.symbol ??
      "";

  String get fromLogoUrl =>
      fromToken?.getLogoUrl() ??
      swapQuote?.fromToken.logoURI ??
      fromToken?.logoUrl ??
      "";

  String get toLogoUrl =>
      toToken?.getLogoUrl() ??
      swapQuote?.toToken.logoURI ??
      toToken?.logoUrl ??
      "";

  String get fromAddress =>
      crossSwapQuote?.fromAddress ??
      swapQuote?.fromToken.address ??
      fromToken?.contractAddress ??
      "";

  String get toAddress =>
      crossSwapQuote?.toAddress ??
      swapQuote?.toToken.address ??
      toToken?.contractAddress ??
      "";
}
