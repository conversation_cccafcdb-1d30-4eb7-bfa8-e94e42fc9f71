import 'package:flutter/material.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:web3auth_flutter/web3auth_flutter.dart';

class LifeCycleManager extends StatefulWidget {
  final Widget child;

  const LifeCycleManager({super.key, required this.child});

  @override
  _LifeCycleManagerState createState() => _LifeCycleManagerState();
}

class _LifeCycleManagerState extends State<LifeCycleManager>
    with WidgetsBindingObserver {
  final _userService = locator<UserService>();
  final _analyticsService = locator<AnalyticsService>();
  final _lifeCycleService = locator<LifeCycleService>();

  DateTime? _dateTimeAppWasInactive;
  bool _isInactive = false;

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
    _lifeCycleService.closeStream();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    _lifeCycleService.addStream(state);
    if (state == AppLifecycleState.inactive) {
      locator<AppSettingsService>().hideBalanceInAppSwitcher(true);
    } else if (state == AppLifecycleState.resumed) {
      try {
        Web3AuthFlutter.setCustomTabsClosed();
      } catch (_) {}

      // _checkUnlockRequired();
      // _reportAudienceData();
      // locator<OnboardExchangeService>().addOrder(null);
      // locator<WalletConnectService>().reinitializeWalletConnect();
      // locator<AppSettingsService>().hideBalanceInAppSwitcher(false);
      // locator<CrossSwapService>().checkUnconfirmedTransactions();
    } else if (state == AppLifecycleState.paused) {
      _isInactive = !_isInactive;
      if (_isInactive) {
        _dateTimeAppWasInactive = DateTime.now();
      }
    }
  }

  Future _checkUnlockRequired() async {
    final navigationService = locator<NavigationService>();
    String currentRoute = navigationService.currentRoute;
    final authenticationService = locator<AuthenticationService>();
    bool isLoggedIn = await authenticationService.isLoggedIn();
    if (isLoggedIn == false) {
      return;
    }

    final passcodeService = locator<PasscodeService>();
    bool hasSecurity = passcodeService.hasLocalPinSetup();

    if (_dateTimeAppWasInactive != null && hasSecurity) {
      if (currentRoute == Routes.cardDetailsView ||
          currentRoute == Routes.showCredentialsView) {
        _requestDeviceUnlock();
      } else {
        Duration durationBeforeLock = passcodeService.getLockDuration;
        var difference = DateTime.now().difference(_dateTimeAppWasInactive!);
        if (difference >= durationBeforeLock) {
          await _requestDeviceUnlock();
        }
      }
    }
    _dateTimeAppWasInactive = null;
    _isInactive = false;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: widget.child,
    );
  }

  Future _reportAudienceData() async {
    await Future.wait([
      _reportUserProperty(),
      _reportTransactionProperty(),
    ]);
  }

  Future _reportUserProperty() async {
    final user = await _userService.getUpdatedUserInfo();
    if (user != null) {
      _analyticsService.reportUserProperty(user);
    }
  }

  Future _reportTransactionProperty() async {
    final response = await locator<CryptoService>().checkRampHistory();
    _analyticsService.setUserProperties(
      name: UserPropertyKey.ramp,
      value: response.toString(),
    );
  }

  Future _requestDeviceUnlock() async {
    var navigationService = locator<NavigationService>();
    String currentRoute = navigationService.currentRoute;
    bool isSecureView = currentRoute == Routes.cardDetailsView ||
        currentRoute == Routes.showCredentialsView;
    if (currentRoute != Routes.passcodeUnlockView) {
      navigationService.navigateToPasscodeUnlockView(
          canPop: false,
          onUnlock: () {
            navigationService.back();
            if (isSecureView) {
              locator<SecureAppService>().secure();
            }
            FocusScope.of(context).unfocus();
          });

      ///un secure view after opening the passcode unlock screen.
      if (isSecureView) {
        locator<SecureAppService>().unSecure();
      }
    }
  }
}
