import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:onboard_wallet/api/onboard_exception.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/enums/enums.dart';
import 'package:onboard_wallet/flavors.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/models/cross_swap/li_fi_quote_response/li_fi_quote_response.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/models/swing/cross_swap_quote/provider_route.dart';
import 'package:onboard_wallet/models/swing/swing.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:web3dart/web3dart.dart';

import '../manager/manager.dart';
import '../models/swing/cross_swap_quote/swing_quote.dart';
import 'crashlytics_service.dart';

class CrossSwapService {
  static const platformBaseUrlMainNet =
      "https://platform.swing.xyz/api/v1/projects/onboard-wallet/";
  static const crossChainSwapBaseUrlMainNet =
      "https://swap.prod.swing.xyz/v0/transfer/";
  static const projectId = "onboard-wallet";
  final BaseOptions _platformBaseOption = BaseOptions(
    baseUrl: platformBaseUrlMainNet,
    connectTimeout: const Duration(milliseconds: 95000),
    receiveTimeout: const Duration(milliseconds: 95000),
    headers: {
      "Content-Type": "application/json",
    },
  );
  late final Dio _platformDio = Dio(_platformBaseOption)
    ..interceptors.add(LogInterceptor(
      requestBody: F.isStaging,
      responseBody: F.isStaging,
    ));

  final BaseOptions _crossChainSwapBaseOption = BaseOptions(
    baseUrl: crossChainSwapBaseUrlMainNet,
    connectTimeout: const Duration(milliseconds: 95000),
    receiveTimeout: const Duration(milliseconds: 95000),
    headers: {
      "Content-Type": "application/json",
    },
  );
  late final Dio _crossChainSwapDio = Dio(_crossChainSwapBaseOption)
    ..interceptors.add(LogInterceptor(
        requestBody: F.isStaging,
        responseBody: F.isStaging,
        logPrint: (object) {
          getLogger('Cross-Chain-Swap-dio').i(object);
        }));
  final crashlyticsService = locator<CrashlyticsService>();
  static const lifi = "https://li.quest/v1/";
  final BaseOptions _lifiBaseOption = BaseOptions(
    baseUrl: lifi,
    connectTimeout: const Duration(milliseconds: 95000),
    receiveTimeout: const Duration(milliseconds: 95000),
    headers: {
      "Content-Type": "application/json",
    },
  );
  late final Dio _lifiDio = Dio(_lifiBaseOption)
    ..interceptors.add(LogInterceptor(
      requestBody: F.isStaging,
      responseBody: F.isStaging,
      logPrint: ((object) {
        getLogger('LiFI-Cross-Chain-Swap-dio').i(object);
      }),
    ));

  final StreamController<CrossSwapTransactionTrackingModel>
      _statusTrackingController =
      StreamController<CrossSwapTransactionTrackingModel>.broadcast();

  // Get the stream (to listen to)
  Stream<CrossSwapTransactionTrackingModel> get statusTrackingStream =>
      _statusTrackingController.stream;

  List<SwingChain> swingChains = [];
  List<LocalToken> swingTokens = [];

  List<CrossSwapChain> lifiSupportedChains = [];
  List<CrossSwapToken> lifiSupportedTokens = [];

  Future getChainAndTokens() async {
    getSupportedChain();
    getSupportedTokens();
  }

  Future getSupportedChain() async {
    try {
      final response = await _platformDio.get('chains');
      Iterable data = response.data;
      final sChains = data.map((e) => SwingChain.fromJson(e));
      swingChains = sChains.toList();
    } catch (e) {
      getLogger(toString()).d(e);
    }
  }

  Future getSupportedTokens() async {
    try {
      final response = await _platformDio.get('tokens');
      Iterable tokenList = response.data;
      List<LocalToken> tokensList = [];
      for (var token in tokenList) {
        final networkId = token["chain"];
        final network = locator<NetworkService>()
            .networks
            .firstWhereOrNull((element) => element.coreNetworkId == networkId);
        tokensList.add(LocalToken.fromJson(token)..network = network);
      }
      swingTokens = tokensList;
    } catch (e) {
      getLogger(toString()).d(e);
    }
  }

  Future<CrossSwapQuote> getRoute({
    required SwingRouteRequestBody body,
    OnboardCrossSwapQuote? selected,
  }) async {
    final request = body.copyWith(projectId: projectId);
    List<Future<Result?>> futures = [];

    // Add futures based on the conditions
    if (selected == null) {
      futures.add(_getSwingRoute(body: request));
      futures.add(_getLifiQuote(body: request));
    } else if (selected is SwingQuote) {
      futures.add(_getSwingRoute(body: request));
    } else if (selected is LiFiQuoteResponse) {
      futures.add(_getLifiQuote(body: request));
    }

    // Execute futures and handle any failures gracefully
    List<Result?> responses = await Future.wait(
      futures.map((future) async {
        try {
          return await future;
        } catch (e) {
          return null; // Return null in case of an error
        }
      }).toList(),
    );

    CrossSwapQuote crossSwapQuote = CrossSwapQuote();

    // Handling the responses based on the conditions
    if (selected == null) {
      // Case when both Swing and LiFi quotes are fetched
      if (responses.isNotEmpty && responses[0] is Result<SwingQuote>) {
        Result<SwingQuote>? swingResponse = responses[0] as Result<SwingQuote>?;
        swingResponse?.when(
          success: (data) {
            crossSwapQuote = crossSwapQuote.copyWith(swingQuote: data);
          },
          failure: (failure) {
            crossSwapQuote = crossSwapQuote.copyWith(swingError: failure);
          },
        );
      }

      if (responses.length > 1 && responses[1] is Result<LiFiQuoteResponse>) {
        Result<LiFiQuoteResponse>? lifiResponse =
            responses[1] as Result<LiFiQuoteResponse>?;
        lifiResponse?.when(
          success: (data) {
            crossSwapQuote = crossSwapQuote.copyWith(liFiQuote: data);
          },
          failure: (failure) {
            crossSwapQuote = crossSwapQuote.copyWith(liFiError: failure);
          },
        );
      }
    } else if (selected is SwingQuote) {
      // Case when only Swing quote is fetched
      if (responses.isNotEmpty && responses[0] is Result<SwingQuote>) {
        Result<SwingQuote>? swingResponse = responses[0] as Result<SwingQuote>?;
        swingResponse?.when(
          success: (data) {
            crossSwapQuote = crossSwapQuote.copyWith(swingQuote: data);
          },
          failure: (failure) {
            crossSwapQuote = crossSwapQuote.copyWith(swingError: failure);
          },
        );
      }
    } else if (selected is LiFiQuoteResponse) {
      // Case when only LiFi quote is fetched
      if (responses.isNotEmpty && responses[0] is Result<LiFiQuoteResponse>) {
        Result<LiFiQuoteResponse>? lifiResponse =
            responses[0] as Result<LiFiQuoteResponse>?;
        lifiResponse?.when(
          success: (data) {
            crossSwapQuote = crossSwapQuote.copyWith(liFiQuote: data);
          },
          failure: (failure) {
            crossSwapQuote = crossSwapQuote.copyWith(liFiError: failure);
          },
        );
      }
    }

    // Determine selected provider based on successful responses
    final swingValue = crossSwapQuote.swingQuote?.selectedRoute?.quote?.amount;
    final lifiValue = crossSwapQuote.liFiQuote?.estimate?.toAmount;
    if (swingValue != null && lifiValue != null) {
      final swingValueAsNum = num.tryParse(swingValue) ?? 0;
      final lifiValueAsNum = num.tryParse(lifiValue) ?? 0;
      if (swingValueAsNum > lifiValueAsNum) {
        crossSwapQuote.setSelectedProvider(crossSwapQuote.swingQuote!);
      } else {
        crossSwapQuote.setSelectedProvider(crossSwapQuote.liFiQuote!);
      }
    } else if (crossSwapQuote.swingQuote != null &&
        crossSwapQuote.hasSwingRoute == true) {
      crossSwapQuote.setSelectedProvider(crossSwapQuote.swingQuote!);
    } else if (crossSwapQuote.liFiQuote != null) {
      crossSwapQuote.setSelectedProvider(crossSwapQuote.liFiQuote!);
    }

    return crossSwapQuote;
  }

  Future<Result<SwingQuote>?> _getSwingRoute(
      {required SwingRouteRequestBody body}) async {
    try {
      Map<String, dynamic> params = body.toJson();
      if (body.fee != null) {
        num feeAsSwingBasePoints = getSwingFeeBasePoints(body.fee ?? 0);
        if (feeAsSwingBasePoints >= 1 && feeAsSwingBasePoints <= 1000) {
          final updatedBody = body.copyWith(fee: feeAsSwingBasePoints);
          params = updatedBody.toJson();
        }
      }
      params.removeWhere((key, value) => value == null);
      final response = await _crossChainSwapDio.get(
        "quote",
        queryParameters: params,
      );
      final data = response.data;
      final swapQuote = SwingQuote.fromJson(data);
      return Result.success(data: swapQuote);
    } on DioException catch (e) {
      final response = e.response;
      if (response != null) {
        if (response.statusCode == 403) {
          crashlyticsService.recordError(e, e.stackTrace);
          return Result.failure(
              error: OnboardExceptions.fromErrorMessage(kUnsupportedCountry));
        }
      }
      return Result.failure(error: OnboardExceptions.fromDioError(e));
    } catch (e) {
      getLogger(toString()).e(e);
      return Result.failure(
          error: OnboardExceptions.fromErrorMessage(e.toString()));
    }
  }

  Future<Result<bool>> isApprovalRequired({
    required CrossSwapQuote crossSwapQuote,
    required String userAddress,
    required String amount,
    required String rpcUrl,
  }) async {
    final selectedQuote = crossSwapQuote.selectedQuote ??
        crossSwapQuote.swingQuote ??
        crossSwapQuote.liFiQuote;
    if (selectedQuote is SwingQuote) {
      return await _checkSwingApproval(
        quote: selectedQuote,
        providerRoute: crossSwapQuote.swingProviderRoute!,
        userAddress: userAddress,
        amount: amount,
      );
    } else if (selectedQuote is LiFiQuoteResponse) {
      Web3Service web3service = locator<Web3Service>();
      LiFiQuoteResponse lifiQuote = selectedQuote;
      final response = await web3service.checkAllowance(
        rpcUrl: rpcUrl,
        tokenAddress: lifiQuote.action?.fromToken?.address ?? "",
        approvalAddress: lifiQuote.estimate?.approvalAddress ?? "",
        walletAddress: locator<WalletService>().getWalletAddress ?? "",
        amount: BigInt.parse(amount),
      );
      return Result.success(data: response != null);
    }
    return Result.failure(
        error: OnboardExceptions.fromErrorMessage("No route"));
  }

  Future<Result<bool>> _checkSwingApproval({
    required SwingQuote quote,
    required ProviderRoute providerRoute,
    required String userAddress,
    required String amount,
  }) async {
    try {
      final param = {
        "fromChain": quote.fromChain?.slug,
        "fromChainId": quote.fromChain?.chainId,
        "tokenSymbol": quote.fromToken?.symbol,
        "tokenAddress": quote.fromToken?.address,
        "bridge": providerRoute.bridge,
        "fromAddress": userAddress,
        "toChain": quote.toChain?.slug,
        "toTokenAddress": quote.toToken?.address,
        "toTokenSymbol": quote.toToken?.symbol,
      };
      final response =
          await _crossChainSwapDio.get("allowance", queryParameters: param);
      final allowance = num.parse(response.data["allowance"]);
      if (allowance == 0) {
        return const Result.success(data: true);
      }
      BigInt allowanceAsBigInt = BigInt.from(allowance);
      BigInt amountAsBigInt = BigInt.from(num.parse(amount));
      var approvalRequired = amountAsBigInt > allowanceAsBigInt;
      return Result.success(data: approvalRequired);
    } on DioException catch (e) {
      getLogger(toString()).e(e);
      return Result.failure(error: OnboardExceptions.fromDioError(e));
    } on Exception catch (e) {
      getLogger(toString()).e(e);
      return Result.failure(error: OnboardExceptions.fromException(e));
    }
  }

  Future<Result<Transaction>> getApprovalCallData({
    required CrossSwapQuote crossSwapQuote,
    required String userAddress,
    required String tokenAmount,
  }) async {
    try {
      OnboardCrossSwapQuote selectedQuote = crossSwapQuote.selectedQuote!;
      if (selectedQuote is SwingQuote) {
        SwingQuote quote = selectedQuote;
        final param = {
          "fromChain": quote.fromChain?.slug,
          "fromChainId": quote.fromChain?.chainId,
          "tokenSymbol": quote.fromToken?.symbol,
          "tokenAddress": quote.fromToken?.address,
          "toChain": quote.toChain?.slug,
          "toChainId": quote.toChain?.chainId,
          "bridge": crossSwapQuote.swingProviderRoute?.bridge,
          "fromAddress": userAddress,
          "tokenAmount": tokenAmount,
          "toTokenAddress": quote.toToken?.address,
          "toTokenSymbol": quote.toToken?.symbol,
        };
        final response =
            await _crossChainSwapDio.get("approve", queryParameters: param);
        final result = CrossSwapApproveResponse.fromJson(response.data);
        if (result.tx != null) {
          if (result.tx!.isNotEmpty) {
            final tx = result.tx!.first;
            final transaction = Transaction(
              data: getData(tx.data),
              to: getAddress(tx.to),
              from: EthereumAddress.fromHex(
                userAddress,
              ),
            );
            return Result.success(
              data: transaction,
            );
          }
        }
      } else if (selectedQuote is LiFiQuoteResponse) {
        LiFiQuoteResponse lifiQuote = selectedQuote;
        Web3Service web3service = locator<Web3Service>();
        final transaction = await web3service.getApprovalTransaction(
          tokenAddress: lifiQuote.action?.fromToken?.address ?? "",
          approvalAddress: lifiQuote.estimate?.approvalAddress ?? "",
          walletAddress: locator<WalletService>().getWalletAddress ?? "",
          amount: BigInt.parse(tokenAmount),
        );
        return Result.success(
          data: transaction!,
        );
      }
      throw Exception("Transaction data is missing");
    } on DioException catch (e) {
      getLogger(toString()).e(e);
      return Result.failure(error: OnboardExceptions.fromDioError(e));
    } on Exception catch (e) {
      getLogger(toString()).e(e);
      return Result.failure(error: OnboardExceptions.fromException(e));
    }
  }

  Future<Result<CrossSwapSendResponse>> send({
    required SwingQuote quote,
    required ProviderRoute providerRoute,
    required String userAddress,
    required String tokenAmount,
    required String toUserAddress,
    num? fee,
    String? partnerAddress,
  }) async {
    try {
      final param = {
        "fromChain": quote.fromChain?.slug,
        "fromChainId": quote.fromChain?.chainId,
        "tokenSymbol": quote.fromToken?.symbol,
        "fromTokenAddress": quote.fromToken?.address,
        "toChain": quote.toChain?.slug,
        "toChainId": quote.toChain?.chainId,
        "toTokenSymbol": quote.toToken?.symbol,
        "toTokenAddress": quote.toToken?.address,
        "fromUserAddress": userAddress,
        "toUserAddress": toUserAddress,
        "tokenAmount": tokenAmount,
        "toTokenAmount": quote.selectedRoute?.quote?.amount,
        "projectId": projectId,
        "route": [
          {
            "bridge": providerRoute.bridge,
            "bridgeTokenAddress": providerRoute.bridgeTokenAddress,
            "name": providerRoute.name,
            "part": providerRoute.part,
          }
        ]
      };
      if (fee != null && partnerAddress != null) {
        param['fee'] = fee;
        param['partner'] = partnerAddress;
      }
      final response = await _crossChainSwapDio.post("send", data: param);
      return Result.success(
          data: CrossSwapSendResponse.fromJson(response.data));
    } on DioException catch (e) {
      getLogger(toString()).e(e);
      return Result.failure(error: OnboardExceptions.fromDioError(e));
    } on Exception catch (e) {
      getLogger(toString()).e(e);
      return Result.failure(error: OnboardExceptions.fromException(e));
    }
  }

  Future<CrossSwapTransaction?> getTransactionById(
      {required String txId, required int provider}) async {
    try {
      if (provider == CrossChainProvider.swing.value) {
        final response = await _platformDio.get("transactions/$txId");
        final data = response.data;
        return CrossSwapTransaction.fromJson(data);
      } else if (provider == CrossChainProvider.lifi.value) {
        final response = await _lifiDio.get("status", queryParameters: {
          "txHash": txId,
        });
        final data = response.data;
        final lifiTrx = LiFiTransaction.fromJson(data);
        return CrossSwapTransaction.fromLiFiTransaction(lifiTrx);
      }
      return null;
    } catch (e) {
      getLogger(toString()).e(e);
      return null;
    }
  }

  Future<bool?> trackStatus({
    required String txId,
    required String txHash,
    required bool isLifi,
  }) async {
    try {
      if (isLifi) {
        return await _trackLiFiStatus(txHash: txHash);
      } else {
        return await _trackSwingStatus(txId: txId, txHash: txHash);
      }
    } catch (e) {
      getLogger(toString()).d(e);
      return null;
    }
  }

  Future<bool?> _trackSwingStatus({
    required String txId,
    required String txHash,
  }) async {
    try {
      final response = await _crossChainSwapDio.get("status", queryParameters: {
        "id": txId,
        "txHash": txHash,
        "projectId": projectId,
      });
      final data = response.data;
      final status = data["status"] as String?;
      const pendingStatuses = [
        'Submitted',
        'Not Sent',
        'Pending Source Chain',
        'Pending Destination Chain',
      ];
      if (!_statusTrackingController.isClosed) {
        _statusTrackingController.add(
          CrossSwapTransactionTrackingModel(
            txId: txId,
            hash: txHash,
            provider: CrossChainProvider.swing.value,
            statusModel: CrossSwapTransactionStatusModel(
              status: mapToCrossSwapStatus(status!),
              statusMessage: "",
              statusRawName: status,
            ),
          ),
        );
      }
      if (pendingStatuses.contains(status)) {
        return false;
      } else {
        final fromChainTxHash = data["fromChainTxHash"] as String;
        final toChainTxHash = data["toChainTxHash"] as String;
        locator<FireStoreService>().saveCrossSwapTransaction(
          transactionId: txId,
          fromChainTxHash: fromChainTxHash,
          toChainTxHash: toChainTxHash,
          provider: CrossChainProvider.swing.value,
        );
        final success = status == "Completed";
        _showToast(success);

        return true;
      }
    } catch (e) {
      getLogger(toString()).d(e);
      return null;
    }
  }

  void _showToast(bool status) {
    final toastManager = locator<ToastManager>();
    if (status) {
      toastManager.showToast(text: S.current.transactionSuccessful);
    } else {
      toastManager.showErrorToast(text: S.current.transactionFailed);
    }
  }

  Future<CrossSwapTransactionStatusModel> getCrossSwapTransactionStatus({
    required String txId,
    required String txHash,
    required bool isLifi,
  }) async {
    if (isLifi) {
      return await _getLiFiStatus(txHash: txHash);
    } else {
      return await _getSwingStatus(txId: txId, txHash: txHash);
    }
  }

  Future<CrossSwapTransactionStatusModel> _getSwingStatus({
    required String txId,
    required String txHash,
  }) async {
    try {
      final response = await _crossChainSwapDio.get("status", queryParameters: {
        "id": txId,
        "txHash": txHash,
        "projectId": projectId,
      });
      final data = response.data;
      final status = data["status"] as String?;
      final errorReason = data["errorReason"] as String?;
      final refundReason = data["refundReason"] as String?;
      return CrossSwapTransactionStatusModel(
        status: mapToCrossSwapStatus(status!),
        statusRawName: status,
        statusMessage: errorReason ?? refundReason,
      );
    } catch (e) {
      getLogger(toString()).d(e);
      return CrossSwapTransactionStatusModel(
        status: CrossSwapTransactionStatus.notFound,
        statusRawName: "NOT FOUND",
      );
    }
  }

  Future<CrossSwapTransactionStatusModel> _getLiFiStatus({
    required String txHash,
  }) async {
    try {
      final response = await _lifiDio.get("status", queryParameters: {
        "txHash": txHash,
      });
      final data = response.data;
      final substatus = data["substatus"] as String?;
      final substatusMessage = data["substatusMessage"] as String?;
      return CrossSwapTransactionStatusModel(
        status: mapToCrossSwapStatus(substatus!),
        statusRawName: substatus,
        statusMessage: substatusMessage,
      );
    } catch (e) {
      getLogger(toString()).d(e);
      return CrossSwapTransactionStatusModel(
        status: CrossSwapTransactionStatus.notFound,
        statusRawName: "NOT FOUND",
      );
    }
  }

  String? getContractAddressOfNativeToken({required String chainId}) {
    try {
      var result =
          swingChains.firstWhereOrNull((element) => element.id == chainId);
      return result?.nativeToken?.address;
    } catch (_) {
      return null;
    }
  }

  Future<bool?> _trackLiFiStatus({
    required String txHash,
  }) async {
    try {
      final response = await _lifiDio.get("status", queryParameters: {
        "txHash": txHash,
      });
      final data = response.data;
      final status = data["status"] as String?;
      if (!_statusTrackingController.isClosed) {
        String? substatus = data["substatus"] as String?;
        final substatusMessage = data["substatusMessage"] as String?;
        if (substatus == "UNKNOWN_ERROR" && status == "PENDING") {
          substatus = "WAIT_DESTINATION_TRANSACTION";
        }
        _statusTrackingController.add(
          CrossSwapTransactionTrackingModel(
            txId: txHash,
            hash: txHash,
            provider: CrossChainProvider.lifi.value,
            statusModel: CrossSwapTransactionStatusModel(
              status: mapToCrossSwapStatus(substatus!),
              statusMessage: substatusMessage,
              statusRawName: substatus,
            ),
          ),
        );
      }
      const pendingStatuses = [
        'PENDING',
      ];
      if (pendingStatuses.contains(status)) {
        return false;
      } else if (status == 'DONE') {
        final sending = data["sending"];
        final fromChainTxHash = sending["txHash"] as String;
        final receiving = data["receiving"];
        final toChainTxHash = receiving["txHash"] as String;
        locator<FireStoreService>().saveCrossSwapTransaction(
          transactionId: txHash,
          fromChainTxHash: fromChainTxHash,
          toChainTxHash: toChainTxHash,
          provider: CrossChainProvider.lifi.value,
        );
        _showToast(true);
        return true;
      } else {
        _showToast(false);
      }
      return false;
    } catch (e) {
      getLogger(toString()).d(e);
      return null;
    }
  }

  void startPolling({
    required String txId,
    required String txHash,
    required bool isLifi,
  }) async {
    locator<FireStoreService>().saveCrossSwapTransaction(
      transactionId: txId,
      fromChainTxHash: txHash,
      toChainTxHash: "",
      provider: isLifi
          ? CrossChainProvider.lifi.value
          : CrossChainProvider.swing.value,
    );
    int attempt = 0;
    const maxAttempt = 20;
    const interval = Duration(seconds: 10);
    Timer.periodic(interval, (timer) async {
      attempt++;
      bool? result =
          await trackStatus(txId: txId, txHash: txHash, isLifi: isLifi);
      if (result == null) {
        timer.cancel();
        await removeTransactionFromPrefs(txId, txHash, isLifi);
        return;
      }
      if (result == false) {
        if (attempt >= maxAttempt) {
          timer.cancel();
          await addTransactionToPrefs(txId, txHash, isLifi);
        } else {
          getLogger(toString()).d("Retrying $txId AND HASH $txHash");
        }
      } else {
        timer.cancel();
        await removeTransactionFromPrefs(txId, txHash, isLifi);
      }
    });
  }

  Future<void> addTransactionToPrefs(
      String id, String txHash, bool isLifi) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> transactions = prefs.getStringList('transactions') ?? [];
    String transaction =
        jsonEncode({'id': id, 'txHash': txHash, 'isLifi': isLifi});
    if (!transactions.contains(transaction)) {
      transactions.add(transaction);
      await prefs.setStringList('transactions', transactions);
    }
  }

  Future<void> removeTransactionFromPrefs(
      String id, String txHash, bool isLifi) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      List<String> transactions = prefs.getStringList('transactions') ?? [];
      String transaction =
          jsonEncode({'id': id, 'txHash': txHash, 'isLifi': isLifi});
      transactions.remove(transaction);
      await prefs.setStringList('transactions', transactions);
    } on Exception catch (e) {
      getLogger(toString()).e(e);
    }
  }

  Future<void> checkUnconfirmedTransactions() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> transactions = prefs.getStringList('transactions') ?? [];
    for (String transaction in transactions) {
      Map<String, dynamic> transactionData = jsonDecode(transaction);
      startPolling(
          txId: transactionData['id'],
          txHash: transactionData['txHash'],
          isLifi: transactionData['isLifi']);
    }
  }

  num getSwingFeeBasePoints(num feePercentage) {
    return feePercentage / 0.01;
  }

  // Lifi
  Future getLifiSupportedChains() async {
    try {
      final response =
          await _lifiDio.get('chains', queryParameters: {"chainTypes": "evm"});
      Map<String, dynamic> data = response.data;
      Iterable chains = data["chains"];
      final sChains = chains.map((e) => CrossSwapChain.fromLiFi(json: e));
      lifiSupportedChains = sChains.toList();
    } catch (e) {
      getLogger(toString()).d(e);
    }
  }

  Future getLifiSupportedTokens({
    required List<CrossSwapChain> chains,
  }) async {
    try {
      final requiredChains = chains.map((e) => e.slug).toList();

      final response = await _lifiDio.get('tokens', queryParameters: {
        "chainTypes": "evm",
        "chains": requiredChains.join(",")
      });
      Map<String, dynamic> data = response.data;
      Iterable tokenList = data.values;
      List<CrossSwapToken> supportedTokens = [];
      for (Iterable list in tokenList) {
        final tokens = list.map((e) => CrossSwapToken.fromLifi(e)).toList();
        supportedTokens.addAll(tokens);
      }
      lifiSupportedTokens = supportedTokens;
    } catch (e) {
      getLogger(toString()).d(e);
    }
  }

  Future<Result<LiFiQuoteResponse>?> _getLifiQuote(
      {required SwingRouteRequestBody body, num? slippage}) async {
    try {
      num fee = 0;
      if (body.fee != null) {
        fee = body.fee! / 100;
      }

      final param = {
        "fromChain": num.tryParse(body.fromChainId!),
        "toChain": num.tryParse(body.toChainId!),
        "fromToken": body.fromTokenAddress,
        "toToken": body.toTokenAddress,
        "fromAddress": body.fromUserAddress,
        "toAddress": body.toUserAddress,
        "fromAmount": body.tokenAmount,
        "integrator": projectId,
        "slippage": slippage,
        "fee": fee,
      };
      final response = await _lifiDio.get('quote', queryParameters: param);
      return Result.success(data: LiFiQuoteResponse.fromJson(response.data));
    } on DioException catch (e) {
      final response = e.response;
      if (response != null) {
        if (response.statusCode == 403) {
          crashlyticsService.recordError(e, e.stackTrace);
          return Result.failure(
              error: OnboardExceptions.fromErrorMessage(kUnsupportedCountry));
        } else {
          final data = response.data;
          String? message = data["message"];
          return Result.failure(error: _formatLifiSwapErrors(message, body));
        }
      }
      return Result.failure(error: OnboardExceptions.fromDioError(e));
    } catch (e) {
      getLogger(toString()).e(e);
      return Result.failure(
          error: OnboardExceptions.fromErrorMessage(e.toString()));
    }
  }

  OnboardExceptions _formatLifiSwapErrors(
      String? message, SwingRouteRequestBody body) {
    if (message == null) {
      return OnboardExceptions.fromErrorMessage(
          S.current.swapGenericErrorMessage);
    } else if (message
        .contains("fromChain must be equal to one of the allowed values")) {
      return OnboardExceptions.fromErrorMessage(
        "${body.fromChain ?? ""} is an incorrect or unsupported chain",
      );
    } else if (message
        .contains("toChain must be equal to one of the allowed values")) {
      return OnboardExceptions.fromErrorMessage(
        "${body.toChain ?? ""} is an incorrect or unsupported chain",
      );
    } else if (message ==
        "None of the available routes could successfully generate a tx") {
      return OnboardExceptions.fromErrorMessage(kLifiUnableToGenerateRoute);
    } else if (message ==
        "querystring must have required property 'fromToken'") {
      return OnboardExceptions.fromErrorMessage(
          S.current.specifyTheTokenYoureSwappingFrom);
    } else if (message == "querystring must have required property 'toToken'") {
      return OnboardExceptions.fromErrorMessage(
          S.current.specifyTheTokenYoureSwappingTo);
    } else if (message ==
        "querystring must have required property 'fromAmount'") {
      return OnboardExceptions.fromErrorMessage(
          S.current.specifyTheAmountOfTokensYouWantToSwap);
    } else {
      return OnboardExceptions.fromErrorMessage(message);
    }
  }
}

class CrossSwapTransactionTrackingModel {
  final String txId;
  final String? hash;
  final int provider;
  final CrossSwapTransactionStatusModel statusModel;

  CrossSwapTransactionTrackingModel({
    required this.txId,
    required this.statusModel,
    this.hash,
    required this.provider,
  });
}
