import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:convert/convert.dart';
import 'package:eth_sig_util/eth_sig_util.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:onboard_wallet/api/onboard_exception.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/utils/utils.dart';
import 'package:web3dart/crypto.dart';
import 'package:web3dart/web3dart.dart';

class Web3Service {
  Web3Client web3client(String rpcUrl) => Web3Client(rpcUrl, http.Client());
  final zeroAddress =
      EthereumAddress.fromHex('******************************************');

  Future<String> _readAbiJson(String path) async {
    return await rootBundle.loadString(path);
  }

  Future<DeployedContract> getContract(
      {required String name,
      required String contractAddress,
      required String jsonAbiPath}) async {
    String jsonData = await _readAbiJson(jsonAbiPath);
    final contract = DeployedContract(ContractAbi.fromJson(jsonData, name),
        EthereumAddress.fromHex(contractAddress));
    return contract;
  }

  Future<TransactionReceipt?> getTransactionReceipt(
      {required String rpcUrl, required String hash}) async {
    TransactionReceipt? transactionReceipt =
        await web3client(rpcUrl).getTransactionReceipt(hash);
    return transactionReceipt;
  }

  String? extractAddressFromPrivateKey(String privateKey) {
    try {
      final ethKey = EthPrivateKey.fromHex(privateKey);
      final ethAddress = ethKey.address;
      return ethAddress.hexEip55;
    } catch (_) {
      return null;
    }
  }

  Future<String?> signMessageWithPrivateKey(
      {required String privateKey,
      required String message,
      required bool isTypedData}) async {
    try {
      if (isTypedData) {
        return signTypedData(
          message: message,
          version: TypedDataVersion.V4,
          privateKey: privateKey,
        );
      } else {
        var hash = keccakUtf8(message);
        return EthSigUtil.signMessage(message: hash, privateKey: privateKey);
      }
    } catch (e) {
      getLogger(toString()).e(e);
      return null;
    }
  }

  String signTypedData(
      {required String privateKey,
      required String message,
      required TypedDataVersion version}) {
    return EthSigUtil.signTypedData(
      jsonData: message,
      version: version,
      privateKey: privateKey,
    );
  }

  Future<String?> signPersonalMessageWithPrivateKey(
      {required String privateKey, required String message}) async {
    var messageAsBytes = hexToBytes(message);
    return EthSigUtil.signPersonalMessage(
        message: messageAsBytes, privateKey: privateKey);
  }

  Future<String?> signTransaction({
    required String rpcUrl,
    required Transaction transaction,
    required String privateKey,
    int? chainId,
    bool fetchChainIdFromNetworkId = false,
  }) async {
    EthPrivateKey ethPrivateKey = EthPrivateKey.fromHex(privateKey);
    var data = await web3client(rpcUrl).signTransaction(
      ethPrivateKey,
      transaction,
      chainId: chainId,
      fetchChainIdFromNetworkId: fetchChainIdFromNetworkId,
    );
    return "0x${hex.encode(data)}";
  }

  Future<BigInt> getGasEstimate({
    EthereumAddress? fromAddress,
    required String rpcUrl,
    EthereumAddress? toAddress,
    EtherAmount? value,
    Uint8List? data,
    EtherAmount? gasPrice,
  }) async {
    try {
      Web3Client web3client = Web3Client(rpcUrl, http.Client());
      return await web3client.estimateGas(
        sender: fromAddress,
        to: toAddress,
        value: value,
        data: data,
        gasPrice: gasPrice,
      );
    } on SocketException {
      throw OnboardExceptions.fromErrorMessage(
          S.current.noInternetConnectionError);
    } catch (e) {
      rethrow;
    }
  }

  Future<EtherAmount> getGasPrice({required String rpcUrl}) async {
    EtherAmount price = await web3client(rpcUrl).getGasPrice();
    return price;
  }

  Future<String?> sendTransaction({
    required String rpcUrl,
    required Transaction transaction,
    required String privateKey,
    int? chainId,
    bool fetchChainIdFromNetworkId = false,
  }) async {
    try {
      EthPrivateKey ethPrivateKey = EthPrivateKey.fromHex(privateKey);
      return await web3client(rpcUrl).sendTransaction(
        ethPrivateKey,
        transaction,
        chainId: chainId,
        fetchChainIdFromNetworkId: fetchChainIdFromNetworkId,
      );
    } on SocketException {
      throw OnboardExceptions.fromErrorMessage(
          S.current.noInternetConnectionError);
    } catch (e) {
      getLogger(toString()).d(e);
      rethrow;
    }
  }

  Future<Transaction> composeTradingWalletContractCall({
    required String smartContractAddress,
    required String contractName,
    required String functionName,
    required List parameters,
    required String fromAddress,
  }) async {
    try {
      DeployedContract contract = await getContract(
          name: contractName,
          contractAddress: smartContractAddress,
          jsonAbiPath: Assets.json.tradingWalletAbi);
      final functions = contract.findFunctionsByName(functionName);
      final function = functions.length == 1
          ? functions.first
          : functions.firstWhere(
              (element) => element.parameters.length == parameters.length);
      Transaction transaction = Transaction.callContract(
        contract: contract,
        function: function,
        parameters: parameters,
        from: EthereumAddress.fromHex(fromAddress),
      );
      return transaction;
    } catch (e) {
      rethrow;
    }
  }

  Future<Transaction> composeContractCall(
      {required String functionName,
      required String contractName,
      required bool isCoin,
      String? contractAddress,
      required String toAddress,
      required String fromAddress,
      required EtherAmount amount,
      required List parameters}) async {
    if (isCoin) {
      return Transaction(
        to: EthereumAddress.fromHex(toAddress),
        from: EthereumAddress.fromHex(fromAddress),
        value: amount,
      );
    } else {
      DeployedContract contract = await getContract(
          name: contractName,
          contractAddress: contractAddress!,
          jsonAbiPath: Assets.json.abi);
      Transaction transaction = Transaction.callContract(
        contract: contract,
        function: contract.function(functionName),
        parameters: parameters,
        from: EthereumAddress.fromHex(fromAddress),
      );
      return transaction;
    }
  }

  Future<int?> getTransactionCount(
      {required String rpcUrl,
      required String address,
      String? atBlock}) async {
    try {
      return await web3client(rpcUrl).getTransactionCount(
          EthereumAddress.fromHex(address),
          atBlock: _getBlock(atBlock));
    } on SocketException {
      throw OnboardExceptions.fromErrorMessage(
          S.current.noInternetConnectionError);
    } catch (e) {
      return null;
    }
  }

  BlockNum _getBlock(String? block) {
    if (block == null) return const BlockNum.current();
    switch (block) {
      case 'earliest':
        return const BlockNum.genesis();
      case 'latest':
        return const BlockNum.current();
      case 'pending':
        return const BlockNum.pending();
      default:
        return const BlockNum.current();
    }
  }

  Future<String?> getBalance({
    required String fromAddress,
    required String rpcUrl,
    int? decimal,
    String? contractAddress,
  }) async {
    try {
      Web3Client web3client = Web3Client(rpcUrl, http.Client());
      if (contractAddress != null) {
        DeployedContract contract = await getContract(
          name: "symbol",
          contractAddress: contractAddress,
          jsonAbiPath: Assets.json.abi,
        );
        var result = await web3client.call(
          contract: contract,
          function: contract.function(ContractName.balanceOf),
          params: [EthereumAddress.fromHex(fromAddress)],
          atBlock: const BlockNum.current(),
        );
        if (result.isNotEmpty) {
          return CryptoUtils().formatEther(result.first, unit: decimal);
        }
        return null;
      } else {
        var balance =
            await web3client.getBalance(EthereumAddress.fromHex(fromAddress));
        return CryptoUtils().formatEther(balance.getInWei, unit: decimal);
      }
    } on SocketException {
      throw OnboardExceptions.fromErrorMessage(
          S.current.noInternetConnectionError);
    } catch (e) {
      return null;
    }
  }

  Future<Transaction?> checkAllowance(
      {required String rpcUrl,
      required String tokenAddress,
      required String approvalAddress,
      required String walletAddress,
      required BigInt amount}) async {
    final tokenAddressAsEthAddress = EthereumAddress.fromHex(tokenAddress);
    // Transactions with the native token don't need approval
    if (tokenAddressAsEthAddress == zeroAddress) {
      return null;
    }
    Web3Client web3client = Web3Client(rpcUrl, http.Client());
    final contract = DeployedContract(
      ContractAbi.fromJson(erc20_Abi, 'ERC20'),
      tokenAddressAsEthAddress,
    );

    final allowanceFunction = contract.function('allowance');
    final approveFunction = contract.function('approve');

    final allowance = await web3client.call(
      contract: contract,
      function: allowanceFunction,
      params: [
        EthereumAddress.fromHex(walletAddress),
        EthereumAddress.fromHex(approvalAddress)
      ],
    );
    if ((allowance.first as BigInt).compareTo(amount) < 0) {
      return Transaction.callContract(
        contract: contract,
        function: approveFunction,
        parameters: [EthereumAddress.fromHex(approvalAddress), amount],
      );
    }
    return null;
  }

  Future<Transaction?> getApprovalTransaction(
      {required String tokenAddress,
      required String approvalAddress,
      required String walletAddress,
      required BigInt amount}) async {
    final tokenAddressAsEthAddress = EthereumAddress.fromHex(tokenAddress);
    // Transactions with the native token don't need approval
    if (tokenAddressAsEthAddress == zeroAddress) {
      return null;
    }
    final contract = DeployedContract(
      ContractAbi.fromJson(erc20_Abi, 'ERC20'),
      tokenAddressAsEthAddress,
    );

    final approveFunction = contract.function('approve');

    return Transaction.callContract(
      contract: contract,
      function: approveFunction,
      parameters: [EthereumAddress.fromHex(approvalAddress), amount],
    );
  }
}

final erc20_Abi = jsonEncode([
  {
    "name": "approve",
    "inputs": [
      {"internalType": "address", "name": "spender", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"}
    ],
    "outputs": [
      {"internalType": "bool", "name": "", "type": "bool"}
    ],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "name": "allowance",
    "inputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "address", "name": "spender", "type": "address"}
    ],
    "outputs": [
      {"internalType": "uint256", "name": "", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  }
]);
